# Codecov configuration for Zenoo RPC
# https://docs.codecov.com/docs/codecov-yaml

coverage:
  precision: 2
  round: down
  range: "70...100"
  
  status:
    project:
      default:
        target: 85%
        threshold: 1%
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error
    patch:
      default:
        target: 80%
        threshold: 5%
        if_no_uploads: error
        if_not_found: success
        if_ci_failed: error

  ignore:
    - "tests/"
    - "docs/"
    - "examples/"
    - "scripts/"
    - "deployment/"
    - "*.md"
    - "*.txt"
    - "*.yml"
    - "*.yaml"
    - "*.toml"
    - "*.cfg"
    - "*.ini"

comment:
  layout: "reach,diff,flags,tree"
  behavior: default
  require_changes: false
  require_base: no
  require_head: yes

github_checks:
  annotations: true

flags:
  unit:
    paths:
      - src/zenoo_rpc/
  integration:
    paths:
      - tests/
