# 🎯 MCP Integration Guide for Zenoo RPC

## 📋 Overview

This guide demonstrates how to use the Model Context Protocol (MCP) integration in Zenoo RPC to extend your Odoo operations with powerful AI tools and external services.

## 🚀 Quick Start

### Basic Setup

```python
import asyncio
from zenoo_rpc import ZenooClient
from zenoo_rpc.mcp import MCPServerConfig, MCPTransportType

async def main():
    # Create Zenoo RPC client
    async with ZenooClient("localhost") as client:
        await client.login("demo", "admin", "admin")
        
        # Setup MCP integration
        await client.setup_mcp_manager([
            MCPServerConfig(
                name="filesystem",
                transport_type=MCPTransportType.STDIO,
                command="mcp-server-filesystem",
                args=["--root", "/data"]
            ),
            MCPServerConfig(
                name="database",
                transport_type=MCPTransportType.HTTP,
                url="http://localhost:8000/mcp"
            )
        ])
        
        # Use MCP tools alongside Odoo operations
        files = await client.mcp_call_tool(
            "filesystem", 
            "list_files", 
            {"path": "/data/reports"}
        )
        
        print(f"Found {len(files)} files in reports directory")

asyncio.run(main())
```

## 🏗️ Architecture Overview

### Core Components

1. **MCPClient**: Individual MCP server connection
2. **MCPManager**: Manages multiple MCP clients
3. **MCPTransport**: Transport layer abstraction
4. **MCPIntegration**: Mixin for ZenooClient

### Transport Types

- **STDIO**: For local MCP servers (command-line tools)
- **HTTP**: For remote MCP servers (web services)
- **SSE**: For Server-Sent Events (streaming)

## 📚 Usage Examples

### 1. File System Operations

```python
# Setup filesystem MCP server
await client.setup_mcp_manager([
    MCPServerConfig(
        name="fs",
        transport_type="stdio",
        command="mcp-server-filesystem",
        args=["--root", "/data"]
    )
])

# List files
files = await client.mcp_call_tool("fs", "list_files", {"path": "/"})

# Read file content
content = await client.mcp_read_resource("fs", "file:///data/config.json")

# Write file
await client.mcp_call_tool("fs", "write_file", {
    "path": "/data/output.txt",
    "content": "Hello from Zenoo RPC!"
})
```

### 2. Database Integration

```python
# Setup database MCP server
await client.setup_mcp_manager([
    MCPServerConfig(
        name="db",
        transport_type="http",
        url="http://localhost:8000/mcp",
        auth={"type": "bearer", "token": "your-token"}
    )
])

# Execute SQL query
results = await client.mcp_call_tool("db", "execute_query", {
    "sql": "SELECT * FROM customers WHERE country = ?",
    "params": ["USA"]
})

# Get database schema
schema = await client.mcp_read_resource("db", "schema://public")
```

### 3. AI-Powered Workflows

```python
# Combine Odoo data with MCP tools and AI
async with client.transaction():
    # Get Odoo partners
    partners = await client.model(ResPartner).filter(
        is_company=True,
        create_date__gte="2024-01-01"
    ).all()
    
    # Use MCP to enrich data
    for partner in partners:
        # Get additional company info from external API
        company_data = await client.mcp_call_tool("api", "lookup_company", {
            "name": partner.name,
            "country": partner.country_id.code
        })
        
        # Use AI to analyze and suggest updates
        if client.ai:
            analysis = await client.ai.query(
                f"Analyze this company data and suggest profile updates: {company_data}"
            )
            
            # Update partner with AI suggestions
            await partner.save({"notes": analysis})
```

### 4. Report Generation

```python
# Generate reports using MCP tools
async def generate_sales_report():
    # Get sales data from Odoo
    sales = await client.model(SaleOrder).filter(
        state="sale",
        date_order__gte="2024-01-01"
    ).all()
    
    # Convert to CSV using MCP
    csv_data = await client.mcp_call_tool("converter", "to_csv", {
        "data": [sale.to_dict() for sale in sales],
        "headers": ["name", "partner_id", "amount_total", "date_order"]
    })
    
    # Save report using filesystem MCP
    await client.mcp_call_tool("fs", "write_file", {
        "path": "/reports/sales_2024.csv",
        "content": csv_data
    })
    
    # Generate PDF report using MCP
    pdf_report = await client.mcp_call_tool("pdf", "generate_report", {
        "template": "sales_summary",
        "data": {"sales": sales, "total": sum(s.amount_total for s in sales)}
    })
    
    return pdf_report
```

## 🔧 Configuration Options

### Server Configuration

```python
config = MCPServerConfig(
    name="my_server",
    transport_type="stdio",  # or "http", "sse"
    
    # STDIO options
    command="mcp-server-command",
    args=["--option", "value"],
    env={"ENV_VAR": "value"},
    cwd="/working/directory",
    
    # HTTP options
    url="http://localhost:8000/mcp",
    auth={"type": "bearer", "token": "token"},
    headers={"Custom-Header": "value"},
    
    # Common options
    timeout=30.0,
    max_retries=3,
    retry_delay=1.0,
    
    # Health check options
    health_check_interval=60.0,
    health_check_timeout=5.0
)
```

### Manager Options

```python
await client.setup_mcp_manager(
    servers=[config1, config2],
    auto_connect=True,           # Connect to all servers immediately
    health_monitoring=True,      # Enable health monitoring
    default_timeout=30.0,        # Default operation timeout
    max_retries=3,              # Default max retries
    health_check_interval=60.0   # Health check interval
)
```

## 🛡️ Error Handling

```python
from zenoo_rpc.mcp import (
    MCPError,
    MCPConnectionError,
    MCPTimeoutError,
    MCPToolError,
    MCPServerNotFoundError
)

try:
    result = await client.mcp_call_tool("server", "tool", {"arg": "value"})
except MCPServerNotFoundError:
    print("MCP server not found")
except MCPConnectionError:
    print("Failed to connect to MCP server")
except MCPTimeoutError:
    print("MCP operation timed out")
except MCPToolError as e:
    print(f"Tool execution failed: {e}")
except MCPError as e:
    print(f"General MCP error: {e}")
```

## 📊 Monitoring and Health Checks

```python
# Check server health
health_status = await client.mcp_health_check_all()
print(f"Server health: {health_status}")

# List connected servers
connected = await client.mcp_get_connected_servers()
print(f"Connected servers: {connected}")

# List available tools
tools = await client.mcp_list_all_tools()
for server, server_tools in tools.items():
    print(f"{server}: {len(server_tools)} tools")

# List available resources
resources = await client.mcp_list_all_resources()
for server, server_resources in resources.items():
    print(f"{server}: {len(server_resources)} resources")
```

## 🎯 Best Practices

### 1. Connection Management

```python
# Use context managers for automatic cleanup
async with ZenooClient("localhost") as client:
    await client.setup_mcp_manager([...])
    # MCP connections automatically cleaned up on exit
```

### 2. Error Handling

```python
# Always handle MCP errors gracefully
try:
    result = await client.mcp_call_tool("server", "tool", args)
except MCPError as e:
    # Log error and continue with fallback
    logger.error(f"MCP operation failed: {e}")
    result = fallback_operation()
```

### 3. Performance Optimization

```python
# Use caching for frequently accessed data
tools = await client.mcp_list_all_tools(use_cache=True)

# Batch operations when possible
async with client.transaction():
    for item in items:
        await process_with_mcp(item)
```

### 4. Security Considerations

```python
# Use secure authentication for HTTP servers
config = MCPServerConfig(
    name="secure_server",
    transport_type="http",
    url="https://secure-server.com/mcp",
    auth={"type": "bearer", "token": os.getenv("MCP_TOKEN")},
    headers={"User-Agent": "ZenooRPC/1.0"}
)
```

## 🚀 Advanced Usage

### Custom MCP Integration

```python
class CustomZenooClient(ZenooClient):
    """Custom client with specialized MCP operations."""
    
    async def sync_with_external_system(self):
        """Sync Odoo data with external system via MCP."""
        # Get Odoo data
        partners = await self.model(ResPartner).all()
        
        # Sync with external system
        for partner in partners:
            external_data = await self.mcp_call_tool(
                "external_api",
                "get_company_info",
                {"name": partner.name}
            )
            
            if external_data:
                await partner.save({
                    "website": external_data.get("website"),
                    "phone": external_data.get("phone")
                })
    
    async def generate_ai_insights(self, model_name: str):
        """Generate AI insights for Odoo model data."""
        # Get model data
        records = await self.model(model_name).limit(100).all()
        
        # Convert to analysis format
        data = await self.mcp_call_tool("converter", "to_analysis_format", {
            "records": [r.to_dict() for r in records],
            "model": model_name
        })
        
        # Generate insights using AI MCP server
        insights = await self.mcp_call_tool("ai_analysis", "generate_insights", {
            "data": data,
            "analysis_type": "business_intelligence"
        })
        
        return insights
```

## 📖 API Reference

See the [MCP API Reference](mcp-api-reference.md) for detailed documentation of all MCP classes and methods.

## 🔗 Related Resources

- [MCP Protocol Specification](https://modelcontextprotocol.io/docs)
- [Available MCP Servers](https://github.com/modelcontextprotocol/servers)
- [Zenoo RPC Documentation](../README.md)
- [AI Integration Guide](ai-integration-guide.md)
