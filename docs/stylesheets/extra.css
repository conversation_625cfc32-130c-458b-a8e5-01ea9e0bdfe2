/* Custom styles for Zenoo RPC documentation */

/* Root variables for consistent theming */
:root {
  --zenoo-primary: #2196f3;
  --zenoo-primary-dark: #1976d2;
  --zenoo-accent: #03dac6;
  --zenoo-success: #4caf50;
  --zenoo-warning: #ff9800;
  --zenoo-error: #f44336;
  --zenoo-code-bg: #f5f5f5;
  --zenoo-border: #e0e0e0;
}

/* Dark theme variables */
[data-md-color-scheme="slate"] {
  --zenoo-code-bg: #2d2d2d;
  --zenoo-border: #404040;
}

/* Custom header styling */
.md-header {
  background: linear-gradient(135deg, var(--zenoo-primary) 0%, var(--zenoo-primary-dark) 100%);
}

/* Logo and title styling */
.md-header__title {
  font-weight: 600;
  font-size: 1.1rem;
}

/* Navigation tabs styling */
.md-tabs {
  background: var(--zenoo-primary-dark);
}

.md-tabs__link {
  font-weight: 500;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.md-tabs__link:hover,
.md-tabs__link--active {
  opacity: 1;
}

/* Code block enhancements */
.highlight {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--zenoo-border);
}

.highlight pre {
  margin: 0;
  padding: 1rem;
  background: var(--zenoo-code-bg);
}

/* Inline code styling */
code {
  background: var(--zenoo-code-bg);
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-size: 0.9em;
  border: 1px solid var(--zenoo-border);
}

/* Admonition styling */
.md-typeset .admonition {
  border-radius: 8px;
  border-left: 4px solid;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.md-typeset .admonition.note {
  border-left-color: var(--zenoo-primary);
}

.md-typeset .admonition.tip {
  border-left-color: var(--zenoo-success);
}

.md-typeset .admonition.warning {
  border-left-color: var(--zenoo-warning);
}

.md-typeset .admonition.danger {
  border-left-color: var(--zenoo-error);
}

/* Table styling */
.md-typeset table:not([class]) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--zenoo-border);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.md-typeset table:not([class]) th {
  background: var(--zenoo-primary);
  color: white;
  font-weight: 600;
}

.md-typeset table:not([class]) tr:nth-child(even) {
  background: rgba(33, 150, 243, 0.05);
}

/* Button styling */
.md-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.md-button--primary {
  background: var(--zenoo-primary);
  border-color: var(--zenoo-primary);
}

.md-button--primary:hover {
  background: var(--zenoo-primary-dark);
  border-color: var(--zenoo-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

/* Search styling */
.md-search__form {
  border-radius: 8px;
}

.md-search__input {
  border-radius: 8px;
}

/* Footer styling */
.md-footer {
  background: linear-gradient(135deg, var(--zenoo-primary-dark) 0%, #1565c0 100%);
}

/* Custom badges */
.zenoo-badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75em;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 4px;
  margin: 0 0.2em;
}

.zenoo-badge--success {
  color: white;
  background-color: var(--zenoo-success);
}

.zenoo-badge--warning {
  color: white;
  background-color: var(--zenoo-warning);
}

.zenoo-badge--error {
  color: white;
  background-color: var(--zenoo-error);
}

.zenoo-badge--info {
  color: white;
  background-color: var(--zenoo-primary);
}

/* Feature grid */
.zenoo-feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.zenoo-feature-card {
  padding: 1.5rem;
  border: 1px solid var(--zenoo-border);
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

[data-md-color-scheme="slate"] .zenoo-feature-card {
  background: var(--md-default-bg-color);
  border-color: var(--zenoo-border);
}

.zenoo-feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.zenoo-feature-card h3 {
  color: var(--zenoo-primary);
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.zenoo-feature-card .zenoo-feature-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--zenoo-primary);
}

/* Comparison table */
.zenoo-comparison {
  margin: 2rem 0;
}

.zenoo-comparison table {
  width: 100%;
}

.zenoo-comparison .check {
  color: var(--zenoo-success);
  font-weight: bold;
}

.zenoo-comparison .cross {
  color: var(--zenoo-error);
  font-weight: bold;
}

/* Code tabs */
.tabbed-set {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--zenoo-border);
}

.tabbed-labels > label {
  background: var(--zenoo-code-bg);
  border-bottom: 1px solid var(--zenoo-border);
  font-weight: 500;
}

.tabbed-labels > label:hover {
  background: var(--zenoo-primary);
  color: white;
}

.tabbed-content {
  background: var(--zenoo-code-bg);
}

/* API documentation styling */
.api-signature {
  background: var(--zenoo-code-bg);
  border: 1px solid var(--zenoo-border);
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  font-family: var(--md-code-font);
}

.api-signature .api-name {
  font-weight: 600;
  color: var(--zenoo-primary);
}

.api-signature .api-params {
  margin-left: 1rem;
  color: #666;
}

[data-md-color-scheme="slate"] .api-signature .api-params {
  color: #ccc;
}

/* Version badge */
.version-badge {
  background: var(--zenoo-accent);
  color: #000;
  padding: 0.2em 0.5em;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: 600;
  margin-left: 0.5em;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .zenoo-feature-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .zenoo-feature-card {
    padding: 1rem;
  }
}

/* Print styles */
@media print {
  .md-header,
  .md-tabs,
  .md-sidebar,
  .md-footer {
    display: none !important;
  }
  
  .md-main__inner {
    margin: 0 !important;
  }
  
  .md-content {
    max-width: none !important;
  }
}
