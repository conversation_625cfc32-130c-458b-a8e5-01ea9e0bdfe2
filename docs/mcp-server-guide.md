# 🎯 MCP Server Guide for Zenoo RPC

## 📋 Overview

The Zenoo RPC MCP Server exposes Odoo operations through the Model Context Protocol (MCP), allowing AI tools like <PERSON>, GPT, and other AI assistants to interact with your Odoo ERP system.

## 🚀 Quick Start

### Installation

```bash
# Install Zenoo RPC with MCP server support
pip install zenoo-rpc[mcp]

# Or install MCP SDK separately
pip install mcp
```

### Basic Usage

```bash
# Start MCP server with default settings (stdio transport)
python -m zenoo_rpc.mcp_server.cli

# Start HTTP server on custom port
python -m zenoo_rpc.mcp_server.cli --transport http --port 8080

# Use custom Odoo connection
python -m zenoo_rpc.mcp_server.cli \
  --odoo-url http://odoo.example.com:8069 \
  --odoo-database production \
  --api-keys "your-api-key-here"
```

### Configuration File

Create `server-config.json`:

```json
{
  "name": "my-odoo-server",
  "transport_type": "http",
  "port": 8080,
  "odoo_url": "http://localhost:8069",
  "odoo_database": "demo",
  "odoo_username": "admin",
  "odoo_password": "admin",
  "security": {
    "auth_method": "api_key",
    "api_keys": ["key1", "key2"],
    "enable_rate_limiting": true,
    "rate_limit_requests": 100,
    "rate_limit_window": 60
  },
  "performance": {
    "enable_caching": true,
    "cache_ttl": 300,
    "max_connections": 50
  },
  "features": {
    "enable_tools": true,
    "enable_resources": true,
    "enable_prompts": true,
    "enable_batch_operations": true
  }
}
```

Then run:
```bash
python -m zenoo_rpc.mcp_server.cli --config server-config.json
```

## 🔧 Server Capabilities

### Tools (Actions AI can perform)

#### **search_records**
Search for records in any Odoo model.

```python
# AI can call this tool
{
  "tool": "search_records",
  "arguments": {
    "model": "res.partner",
    "domain": [["is_company", "=", true]],
    "fields": ["name", "email", "phone"],
    "limit": 10,
    "order": "name ASC"
  }
}
```

#### **get_record**
Get a specific record by ID.

```python
{
  "tool": "get_record",
  "arguments": {
    "model": "sale.order",
    "record_id": 123,
    "fields": ["name", "partner_id", "amount_total"]
  }
}
```

#### **create_record**
Create a new record.

```python
{
  "tool": "create_record",
  "arguments": {
    "model": "res.partner",
    "values": {
      "name": "New Company",
      "email": "<EMAIL>",
      "is_company": true
    }
  }
}
```

#### **update_record**
Update an existing record.

```python
{
  "tool": "update_record",
  "arguments": {
    "model": "res.partner",
    "record_id": 123,
    "values": {
      "phone": "******-0123",
      "website": "https://example.com"
    }
  }
}
```

#### **delete_record**
Delete a record.

```python
{
  "tool": "delete_record",
  "arguments": {
    "model": "res.partner",
    "record_id": 123
  }
}
```

### Resources (Data AI can access)

#### **odoo://models**
List all available Odoo models.

#### **odoo://model/{model_name}**
Get information about a specific model.
- Example: `odoo://model/res.partner`

#### **odoo://record/{model_name}/{record_id}**
Get a specific record as a resource.
- Example: `odoo://record/sale.order/123`

#### **odoo://search/{model_name}/{domain}**
Search results as a resource.
- Example: `odoo://search/res.partner/[["is_company","=",true]]`

### Prompts (Templates AI can use)

#### **analyze_data**
Generate analysis prompts for Odoo data.

```python
{
  "prompt": "analyze_data",
  "arguments": {
    "model": "sale.order",
    "analysis_type": "revenue_trends"
  }
}
```

## 🛡️ Security Configuration

### Authentication Methods

#### **API Key Authentication (Recommended)**
```json
{
  "security": {
    "auth_method": "api_key",
    "api_keys": ["secure-key-1", "secure-key-2"],
    "permission_mapping": {
      "secure-key-1": ["read", "write"],
      "secure-key-2": ["read"]
    }
  }
}
```

#### **Basic Authentication**
```json
{
  "security": {
    "auth_method": "basic",
    "enable_permissions": true
  }
}
```

#### **No Authentication (Development Only)**
```json
{
  "security": {
    "auth_method": "none"
  }
}
```

### Rate Limiting

```json
{
  "security": {
    "enable_rate_limiting": true,
    "rate_limit_requests": 100,
    "rate_limit_window": 60
  }
}
```

### Input Validation

```json
{
  "security": {
    "enable_input_validation": true,
    "max_request_size": 10485760
  }
}
```

## ⚡ Performance Optimization

### Caching

```json
{
  "performance": {
    "enable_caching": true,
    "cache_ttl": 300,
    "cache_max_size": 1000
  }
}
```

### Connection Pooling

```json
{
  "performance": {
    "max_connections": 100,
    "connection_timeout": 30,
    "max_concurrent_requests": 50
  }
}
```

## 🔌 Transport Types

### STDIO (Default)
Best for local development and desktop AI clients.

```bash
python -m zenoo_rpc.mcp_server.cli --transport stdio
```

### HTTP
Best for remote access and web-based AI tools.

```bash
python -m zenoo_rpc.mcp_server.cli --transport http --port 8080
```

### SSE (Server-Sent Events)
Best for streaming applications.

```bash
python -m zenoo_rpc.mcp_server.cli --transport sse --port 8080
```

## 🌍 Environment Variables

```bash
# Server configuration
export MCP_SERVER_NAME="my-odoo-server"
export MCP_TRANSPORT_TYPE="http"
export MCP_HOST="0.0.0.0"
export MCP_PORT="8080"

# Odoo connection
export ODOO_URL="http://localhost:8069"
export ODOO_DATABASE="production"
export ODOO_USERNAME="admin"
export ODOO_PASSWORD="secure-password"

# Security
export MCP_AUTH_METHOD="api_key"
export MCP_API_KEYS="key1,key2,key3"

# Performance
export MCP_ENABLE_CACHING="true"
export MCP_CACHE_TTL="600"
export MCP_MAX_CONNECTIONS="100"

# Features
export MCP_ENABLE_TOOLS="true"
export MCP_ENABLE_RESOURCES="true"
export MCP_ENABLE_PROMPTS="true"

# Logging
export MCP_LOG_LEVEL="INFO"
```

## 🤖 AI Client Integration

### Claude Desktop

Add to your Claude Desktop config:

```json
{
  "mcpServers": {
    "odoo": {
      "command": "python",
      "args": ["-m", "zenoo_rpc.mcp_server.cli"],
      "env": {
        "ODOO_URL": "http://localhost:8069",
        "ODOO_DATABASE": "demo",
        "MCP_API_KEYS": "your-api-key"
      }
    }
  }
}
```

### Custom AI Tools

```python
import asyncio
from mcp.client.stdio import stdio_client

async def connect_to_odoo_mcp():
    # Connect to Zenoo RPC MCP Server
    async with stdio_client([
        "python", "-m", "zenoo_rpc.mcp_server.cli"
    ]) as (read, write):
        # Use MCP client to interact with Odoo
        # Implementation depends on your MCP client library
        pass

asyncio.run(connect_to_odoo_mcp())
```

## 📊 Monitoring and Logging

### Health Checks

```bash
# Check server status (HTTP transport only)
curl http://localhost:8080/health

# Check via MCP tools
# Use list_models resource to verify connectivity
```

### Logging Configuration

```json
{
  "log_level": "INFO",
  "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
}
```

### Metrics

The server provides metrics for:
- Active sessions
- Request rates
- Tool execution times
- Error rates
- Cache hit rates

## 🚀 Production Deployment

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8080

CMD ["python", "-m", "zenoo_rpc.mcp_server.cli", "--transport", "http", "--host", "0.0.0.0", "--port", "8080"]
```

### Systemd Service

```ini
[Unit]
Description=Zenoo RPC MCP Server
After=network.target

[Service]
Type=simple
User=zenoo
WorkingDirectory=/opt/zenoo-rpc
ExecStart=/opt/zenoo-rpc/venv/bin/python -m zenoo_rpc.mcp_server.cli --config /etc/zenoo-rpc/server.json
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### Nginx Reverse Proxy

```nginx
server {
    listen 80;
    server_name odoo-mcp.example.com;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔧 Troubleshooting

### Common Issues

#### Connection Refused
```bash
# Check if Odoo is running
curl http://localhost:8069/web/database/selector

# Check server logs
python -m zenoo_rpc.mcp_server.cli --log-level DEBUG
```

#### Authentication Errors
```bash
# Verify API keys
export MCP_API_KEYS="your-correct-api-key"

# Test with no auth (development only)
python -m zenoo_rpc.mcp_server.cli --config '{"security": {"auth_method": "none"}}'
```

#### Performance Issues
```bash
# Enable caching
export MCP_ENABLE_CACHING="true"

# Increase connection pool
export MCP_MAX_CONNECTIONS="200"

# Monitor with debug logging
export MCP_LOG_LEVEL="DEBUG"
```

## 📖 API Reference

See the [MCP Server API Reference](mcp-server-api.md) for detailed documentation of all server capabilities and configuration options.

## 🔗 Related Resources

- [MCP Protocol Specification](https://modelcontextprotocol.io/docs)
- [Zenoo RPC Documentation](../README.md)
- [MCP Client Integration Guide](mcp-integration-guide.md)
- [Security Best Practices](security-guide.md)
