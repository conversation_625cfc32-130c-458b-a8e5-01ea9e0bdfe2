
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>zenoo_rpc vs odoorpc Performance Benchmark Report</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 3px solid #007acc; }
        .header h1 { color: #007acc; margin: 0; font-size: 2.5em; }
        .header .subtitle { color: #666; font-size: 1.2em; margin-top: 10px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 40px; }
        .summary-card { background: linear-gradient(135deg, #007acc, #0099ff); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; font-size: 1.1em; }
        .summary-card .value { font-size: 2.5em; font-weight: bold; margin: 10px 0; }
        .summary-card .unit { font-size: 0.9em; opacity: 0.9; }
        .section { margin-bottom: 40px; }
        .section h2 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
        .scenario-results { margin-bottom: 30px; padding: 20px; background: #f9f9f9; border-radius: 8px; }
        .scenario-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .scenario-title { font-size: 1.3em; font-weight: bold; color: #333; }
        .performance-grade { padding: 5px 15px; border-radius: 20px; color: white; font-weight: bold; }
        .grade-a { background: #28a745; }
        .grade-b { background: #ffc107; color: #333; }
        .grade-c { background: #fd7e14; }
        .grade-d { background: #dc3545; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .metric-card { background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #007acc; }
        .metric-label { font-size: 0.9em; color: #666; margin-bottom: 5px; }
        .metric-value { font-size: 1.4em; font-weight: bold; color: #333; }
        .improvement { color: #28a745; font-weight: bold; }
        .degradation { color: #dc3545; font-weight: bold; }
        .comparison-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .comparison-table th, .comparison-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .comparison-table th { background-color: #007acc; color: white; }
        .comparison-table tr:nth-child(even) { background-color: #f2f2f2; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 5px solid #007acc; }
        .recommendations ul { margin: 0; padding-left: 20px; }
        .recommendations li { margin-bottom: 8px; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Performance Benchmark Report</h1>
            <div class="subtitle">zenoo_rpc vs odoorpc Comprehensive Analysis</div>
            <div style="margin-top: 15px; color: #666;">
                Generated: 2025-07-27 10:07:51 | Report ID: benchmark_1753585671
            </div>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>Overall Performance</h3>
                <div class="value">63.4%</div>
                <div class="unit">Improvement</div>
            </div>
            <div class="summary-card">
                <h3>Best Scenario</h3>
                <div class="value">76.3%</div>
                <div class="unit">crud_light_load</div>
            </div>
            <div class="summary-card">
                <h3>Avg Throughput Gain</h3>
                <div class="value">2722.0%</div>
                <div class="unit">Higher ops/sec</div>
            </div>
            <div class="summary-card">
                <h3>Scenarios Tested</h3>
                <div class="value">6</div>
                <div class="unit">Test Cases</div>
            </div>
        </div>
        
        <div class="section">
            <h2>📊 Detailed Scenario Results</h2>
            
            <div class="scenario-results">
                <div class="scenario-header">
                    <div class="scenario-title">Crud Light Load</div>
                    <div class="performance-grade grade-b">Grade: B</div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Response Time Improvement</div>
                        <div class="metric-value improvement">
                            +76.3%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Throughput Improvement</div>
                        <div class="metric-value improvement">
                            +30.1%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Avg Response</div>
                        <div class="metric-value">29.0ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Avg Response</div>
                        <div class="metric-value">122.4ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Success Rate</div>
                        <div class="metric-value">76.0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Success Rate</div>
                        <div class="metric-value">100.0%</div>
                    </div>
                </div>
            </div>
            
            <div class="scenario-results">
                <div class="scenario-header">
                    <div class="scenario-title">Crud Medium Load</div>
                    <div class="performance-grade grade-a">Grade: A</div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Response Time Improvement</div>
                        <div class="metric-value improvement">
                            +68.7%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Throughput Improvement</div>
                        <div class="metric-value improvement">
                            +495.0%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Avg Response</div>
                        <div class="metric-value">52.4ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Avg Response</div>
                        <div class="metric-value">167.3ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Success Rate</div>
                        <div class="metric-value">75.0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Success Rate</div>
                        <div class="metric-value">100.0%</div>
                    </div>
                </div>
            </div>
            
            <div class="scenario-results">
                <div class="scenario-header">
                    <div class="scenario-title">Crud Heavy Load</div>
                    <div class="performance-grade grade-a">Grade: A</div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Response Time Improvement</div>
                        <div class="metric-value improvement">
                            +54.1%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Throughput Improvement</div>
                        <div class="metric-value improvement">
                            +3277.0%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Avg Response</div>
                        <div class="metric-value">327.9ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Avg Response</div>
                        <div class="metric-value">715.0ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Success Rate</div>
                        <div class="metric-value">80.0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Success Rate</div>
                        <div class="metric-value">100.0%</div>
                    </div>
                </div>
            </div>
            
            <div class="scenario-results">
                <div class="scenario-header">
                    <div class="scenario-title">Stress Concurrent Reads</div>
                    <div class="performance-grade grade-a">Grade: A</div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Response Time Improvement</div>
                        <div class="metric-value improvement">
                            +57.3%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Throughput Improvement</div>
                        <div class="metric-value improvement">
                            +11616.8%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Avg Response</div>
                        <div class="metric-value">121.1ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Avg Response</div>
                        <div class="metric-value">283.9ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Success Rate</div>
                        <div class="metric-value">100.0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Success Rate</div>
                        <div class="metric-value">100.0%</div>
                    </div>
                </div>
            </div>
            
            <div class="scenario-results">
                <div class="scenario-header">
                    <div class="scenario-title">Memory Large Datasets</div>
                    <div class="performance-grade grade-a">Grade: A</div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Response Time Improvement</div>
                        <div class="metric-value improvement">
                            +51.0%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Throughput Improvement</div>
                        <div class="metric-value improvement">
                            +588.0%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Avg Response</div>
                        <div class="metric-value">1021.0ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Avg Response</div>
                        <div class="metric-value">2083.7ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Success Rate</div>
                        <div class="metric-value">100.0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Success Rate</div>
                        <div class="metric-value">100.0%</div>
                    </div>
                </div>
            </div>
            
            <div class="scenario-results">
                <div class="scenario-header">
                    <div class="scenario-title">Workflow Sales Process</div>
                    <div class="performance-grade grade-a">Grade: A</div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-label">Response Time Improvement</div>
                        <div class="metric-value improvement">
                            +72.7%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Throughput Improvement</div>
                        <div class="metric-value improvement">
                            +325.2%
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Avg Response</div>
                        <div class="metric-value">50.1ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Avg Response</div>
                        <div class="metric-value">183.5ms</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">zenoo_rpc Success Rate</div>
                        <div class="metric-value">100.0%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">odoorpc Success Rate</div>
                        <div class="metric-value">100.0%</div>
                    </div>
                </div>
            </div>
            
        </div>
        
        <div class="section">
            <h2>📈 Performance Comparison Table</h2>
            
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Scenario</th>
                    <th>zenoo_rpc Avg (ms)</th>
                    <th>odoorpc Avg (ms)</th>
                    <th>Response Improvement</th>
                    <th>zenoo_rpc Throughput</th>
                    <th>odoorpc Throughput</th>
                    <th>Throughput Improvement</th>
                    <th>Grade</th>
                </tr>
            </thead>
            <tbody>
                
                <tr>
                    <td>Crud Light Load</td>
                    <td>29.0ms</td>
                    <td>122.4ms</td>
                    <td class="improvement">
                        +76.3%
                    </td>
                    <td>5.8</td>
                    <td>4.5</td>
                    <td class="improvement">
                        +30.1%
                    </td>
                    <td>B</td>
                </tr>
                
                <tr>
                    <td>Crud Medium Load</td>
                    <td>52.4ms</td>
                    <td>167.3ms</td>
                    <td class="improvement">
                        +68.7%
                    </td>
                    <td>35.4</td>
                    <td>6.0</td>
                    <td class="improvement">
                        +495.0%
                    </td>
                    <td>A</td>
                </tr>
                
                <tr>
                    <td>Crud Heavy Load</td>
                    <td>327.9ms</td>
                    <td>715.0ms</td>
                    <td class="improvement">
                        +54.1%
                    </td>
                    <td>47.2</td>
                    <td>1.4</td>
                    <td class="improvement">
                        +3277.0%
                    </td>
                    <td>A</td>
                </tr>
                
                <tr>
                    <td>Stress Concurrent Reads</td>
                    <td>121.1ms</td>
                    <td>283.9ms</td>
                    <td class="improvement">
                        +57.3%
                    </td>
                    <td>412.6</td>
                    <td>3.5</td>
                    <td class="improvement">
                        +11616.8%
                    </td>
                    <td>A</td>
                </tr>
                
                <tr>
                    <td>Memory Large Datasets</td>
                    <td>1021.0ms</td>
                    <td>2083.7ms</td>
                    <td class="improvement">
                        +51.0%
                    </td>
                    <td>3.3</td>
                    <td>0.5</td>
                    <td class="improvement">
                        +588.0%
                    </td>
                    <td>A</td>
                </tr>
                
                <tr>
                    <td>Workflow Sales Process</td>
                    <td>50.1ms</td>
                    <td>183.5ms</td>
                    <td class="improvement">
                        +72.7%
                    </td>
                    <td>22.8</td>
                    <td>5.4</td>
                    <td class="improvement">
                        +325.2%
                    </td>
                    <td>A</td>
                </tr>
                
            </tbody>
        </table>
        
        </div>
        
        <div class="section">
            <h2>💡 Recommendations</h2>
            <div class="recommendations">
                <ul><li>🚀 Immediate migration to zenoo_rpc recommended - significant performance gains demonstrated</li><li>🔧 Enable intelligent caching for frequently accessed data</li><li>📊 Implement batch operations for bulk data processing</li><li>🔄 Use connection pooling for high-concurrency scenarios</li><li>📈 Set up performance monitoring in production</li><li>🛡️ Implement circuit breaker patterns for resilience</li><li>⚙️ Optimize async/await usage for maximum performance benefits</li><li>📋 Conduct load testing before production deployment</li><li>🔍 Monitor memory usage patterns and optimize as needed</li></ul>
            </div>
        </div>
        
        <div class="footer">
            <p>Report generated by zenoo_rpc Performance Benchmark Suite</p>
            <p>For detailed analysis and raw data, see accompanying JSON and CSV files</p>
        </div>
    </div>
</body>
</html>
        