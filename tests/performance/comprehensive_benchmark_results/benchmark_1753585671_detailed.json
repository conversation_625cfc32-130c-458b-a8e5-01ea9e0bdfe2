{"metadata": {"report_id": "benchmark_1753585671", "timestamp": "2025-07-27T10:07:51.211895", "generator": "zenoo_rpc_benchmark", "version": "1.0.0"}, "test_configuration": {"config": {"use_real_server": false, "scenarios_to_run": ["crud_light_load", "crud_medium_load", "crud_heavy_load", "stress_concurrent_reads", "memory_large_datasets", "workflow_sales_process"], "include_stress_tests": true, "include_endurance_tests": false, "generate_html_report": true, "generate_charts": false, "output_dir": "comprehensive_benchmark_results"}, "test_duration": 469.054574, "scenarios_run": 6, "timestamp": "2025-07-27T10:07:51.211923"}, "results": {"crud_light_load": {"scenario": {"name": "CRUD Light Load", "description": "Basic CRUD operations with minimal load", "iterations": 50, "concurrent_users": 1, "data_size": "small", "complexity": "simple", "duration_seconds": null, "ramp_up_seconds": 0, "think_time_ms": 100, "error_threshold": 5.0}, "zenoo_rpc": {"operation": "CRUD Light Load", "library": "zenoo_rpc", "test_id": "CRUD Light Load_zenoo_rpc_1753585671", "timestamp": "2025-07-27T10:07:51.211946", "summary": {"total_operations": 50, "success_count": 38, "success_rate": 76.0, "error_rate": 24.0, "total_duration": 6.528984, "throughput": 5.820201121644653}, "response_times": {"avg": 28.970990082016215, "median": 31.05835450696759, "std_dev": 18.449517216099952, "coefficient_of_variation": 0.6368272939195309, "percentiles": {"p50": 31.05835450696759, "p75": 48.03205250937026, "p90": 51.05578778893687, "p95": 51.081637808238156, "p99": 51.941615344258025, "p99.9": 52.66334845114035}, "outliers_count": 12, "min": 0.010541989468038082, "max": 52.743541018571705}, "system_resources": {"avg_cpu_usage": 2.0504950495049505, "avg_memory_usage": 48.773050742574256, "memory_peak": 52.671875, "memory_growth": 52.671875}, "network": {"total_bytes_transferred": 2242560, "avg_request_size": 31555.36842105263, "avg_response_size": 27459.36842105263}, "errors": {"total_errors": 12, "timeouts": 0, "connection_errors": 0, "error_details": [{"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:51.598608"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:52.115966"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:52.633559"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:53.151521"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:53.669558"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:54.186485"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:54.705975"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:55.251127"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:55.768466"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:56.286527"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:56.803582"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:07:57.324299"}]}, "latency_distribution": {"bins": [0.010541989468038082, 2.6471919409232214, 5.283841892378405, 7.920491843833588, 10.557141795288771, 13.193791746743955, 15.830441698199138, 18.46709164965432, 21.103741601109505, 23.740391552564688, 26.37704150401987, 29.013691455475055, 31.650341406930238, 34.28699135838542, 36.923641309840605, 39.56029126129579, 42.19694121275097, 44.833591164206155, 47.47024111566134, 50.10689106711652, 52.743541018571705], "counts": [12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 0, 0, 1, 0, 0, 0, 0, 13], "bin_centers": [1.3288669651956297, 3.965516916650813, 6.602166868105996, 9.23881681956118, 11.875466771016363, 14.512116722471546, 17.14876667392673, 19.785416625381913, 22.422066576837096, 25.05871652829228, 27.695366479747463, 30.332016431202646, 32.96866638265783, 35.60531633411301, 38.241966285568196, 40.87861623702338, 43.51526618847856, 46.151916139933746, 48.78856609138893, 51.42521604284411]}}, "odoorpc": {"operation": "CRUD Light Load", "library": "odoorpc", "test_id": "CRUD Light Load_odoorpc_1753585677", "timestamp": "2025-07-27T10:07:57.762972", "summary": {"total_operations": 50, "success_count": 50, "success_rate": 100.0, "error_rate": 0.0, "total_duration": 11.179265, "throughput": 4.472565951339377}, "response_times": {"avg": 122.35164911719039, "median": 112.97533300239593, "std_dev": 22.099448398292232, "coefficient_of_variation": 0.18062239910738778, "percentiles": {"p50": 112.97533300239593, "p75": 125.73458302358631, "p90": 158.81741251214407, "p95": 160.0284686050145, "p99": 160.03557184012607, "p99.9": 160.04056970705278}, "outliers_count": 9, "min": 100.53079196950421, "max": 160.0411250256002}, "system_resources": {"avg_cpu_usage": 0.920253164556962, "avg_memory_usage": 40.67306170886076, "memory_peak": 42.4375, "memory_growth": 42.4375}, "network": {"total_bytes_transferred": 39606272, "avg_request_size": 714936.32, "avg_response_size": 77189.12}, "errors": {"total_errors": 0, "timeouts": 0, "connection_errors": 0, "error_details": []}, "latency_distribution": {"bins": [100.53079196950421, 103.506308622309, 106.4818252751138, 109.4573419279186, 112.4328585807234, 115.4083752335282, 118.383891886333, 121.3594085391378, 124.3349251919426, 127.3104418447474, 130.2859584975522, 133.261475150357, 136.2369918031618, 139.2125084559666, 142.1880251087714, 145.1635417615762, 148.139058414381, 151.1145750671858, 154.0900917199906, 157.0656083727954, 160.0411250256002], "counts": [4, 21, 0, 0, 0, 0, 1, 4, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 9], "bin_centers": [102.0185502959066, 104.9940669487114, 107.9695836015162, 110.945100254321, 113.9206169071258, 116.8961335599306, 119.8716502127354, 122.8471668655402, 125.822683518345, 128.7982001711498, 131.7737168239546, 134.7492334767594, 137.7247501295642, 140.700266782369, 143.6757834351738, 146.6513000879786, 149.6268167407834, 152.6023333935882, 155.577850046393, 158.5533666991978]}}, "comparison": {"response_time_improvement_percent": 76.32153690526286, "throughput_improvement_percent": 30.131141384325623, "error_rate_improvement_percent": -24.0, "memory_improvement_percent": -19.914874104372846, "statistical_significance": {"error": "scipy not available for statistical analysis", "significant": "True"}, "performance_grade": "B"}}, "crud_medium_load": {"scenario": {"name": "CRUD Medium Load", "description": "CRUD operations with moderate concurrent load", "iterations": 100, "concurrent_users": 5, "data_size": "medium", "complexity": "medium", "duration_seconds": null, "ramp_up_seconds": 0, "think_time_ms": 50, "error_threshold": 5.0}, "zenoo_rpc": {"operation": "CRUD Medium Load", "library": "zenoo_rpc", "test_id": "CRUD Medium Load_zenoo_rpc_1753585689", "timestamp": "2025-07-27T10:08:09.000254", "summary": {"total_operations": 100, "success_count": 75, "success_rate": 75.0, "error_rate": 25.0, "total_duration": 2.115661, "throughput": 35.44991376217646}, "response_times": {"avg": 52.38495206751395, "median": 41.356312489369884, "std_dev": 46.01196565418191, "coefficient_of_variation": 0.8783431851742747, "percentiles": {"p50": 41.356312489369884, "p75": 81.31960401078686, "p90": 121.172050293535, "p95": 122.13486251712308, "p99": 135.64881264814176, "p99.9": 135.67301847075578}, "outliers_count": 0, "min": 0.0010840012691915035, "max": 135.67570800660178}, "system_resources": {"avg_cpu_usage": 0.5523809523809524, "avg_memory_usage": 34.741815476190474, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 25, "timeouts": 0, "connection_errors": 0, "error_details": [{"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.404016"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.404054"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.404064"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.404071"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.404107"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.811340"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.811356"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.811363"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.811367"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:09.811371"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.218566"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.218649"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.218700"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.218710"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.218716"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.626274"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.626294"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.626301"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.626306"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:10.626311"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:11.033927"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:11.033951"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:11.033961"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:11.033966"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:11.033971"}]}, "latency_distribution": {"bins": [0.0010840012691915035, 6.784815201535821, 13.56854640180245, 20.35227760206908, 27.13600880233571, 33.91974000260234, 40.70347120286897, 47.4872024031356, 54.27093360340223, 61.05466480366886, 67.83839600393549, 74.62212720420212, 81.40585840446874, 88.18958960473537, 94.973320805002, 101.75705200526863, 108.54078320553526, 115.32451440580189, 122.10824560606852, 128.89197680633515, 135.67570800660178], "counts": [25, 0, 0, 0, 25, 0, 0, 20, 0, 0, 5, 0, 0, 0, 0, 0, 0, 20, 0, 5], "bin_centers": [3.3929496014025062, 10.176680801669136, 16.960412001935765, 23.744143202202395, 30.527874402469024, 37.31160560273565, 44.09533680300228, 50.87906800326891, 57.66279920353554, 64.44653040380217, 71.2302616040688, 78.01399280433543, 84.79772400460206, 91.58145520486869, 98.36518640513532, 105.14891760540195, 111.93264880566858, 118.7163800059352, 125.50011120620184, 132.28384240646847]}}, "odoorpc": {"operation": "CRUD Medium Load", "library": "odoorpc", "test_id": "CRUD Medium Load_odoorpc_1753585691", "timestamp": "2025-07-27T10:08:11.195456", "summary": {"total_operations": 100, "success_count": 100, "success_rate": 100.0, "error_rate": 0.0, "total_duration": 16.783163, "throughput": 5.958352427370217}, "response_times": {"avg": 167.26057505933568, "median": 137.91477048653178, "std_dev": 70.20842772612926, "coefficient_of_variation": 0.4197547909973574, "percentiles": {"p50": 137.91477048653178, "p75": 190.18372928258032, "p90": 284.27822560770437, "p95": 285.0351541303098, "p99": 285.04764251702, "p99.9": 285.0492017788929}, "outliers_count": 0, "min": 100.22166697308421, "max": 285.0493750302121}, "system_resources": {"avg_cpu_usage": 0.3148148148148148, "avg_memory_usage": 23.88840663580247, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 0, "timeouts": 0, "connection_errors": 0, "error_details": []}, "latency_distribution": {"bins": [100.22166697308421, 109.4630523759406, 118.704437778797, 127.9458231816534, 137.1872085845098, 146.42859398736618, 155.66997939022258, 164.91136479307897, 174.15275019593537, 183.39413559879176, 192.63552100164816, 201.87690640450455, 211.11829180736095, 220.35967721021734, 229.60106261307374, 238.84244801593013, 248.08383341878653, 257.3252188216429, 266.5666042244993, 275.8079896273557, 285.0493750302121], "counts": [25, 0, 25, 0, 0, 8, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25], "bin_centers": [104.84235967451241, 114.0837450773688, 123.3251304802252, 132.5665158830816, 141.807901285938, 151.04928668879438, 160.29067209165078, 169.53205749450717, 178.77344289736357, 188.01482830021996, 197.25621370307636, 206.49759910593275, 215.73898450878914, 224.98036991164554, 234.22175531450193, 243.46314071735833, 252.70452612021472, 261.9459115230711, 271.1872969259275, 280.4286823287839]}}, "comparison": {"response_time_improvement_percent": 68.68063376624744, "throughput_improvement_percent": 494.9616810065507, "error_rate_improvement_percent": -25.0, "memory_improvement_percent": -45.43379140290415, "statistical_significance": {"error": "scipy not available for statistical analysis", "significant": "True"}, "performance_grade": "A"}}, "crud_heavy_load": {"scenario": {"name": "CRUD Heavy Load", "description": "CRUD operations under heavy concurrent load", "iterations": 200, "concurrent_users": 20, "data_size": "large", "complexity": "complex", "duration_seconds": null, "ramp_up_seconds": 0, "think_time_ms": 10, "error_threshold": 5.0}, "zenoo_rpc": {"operation": "CRUD Heavy Load", "library": "zenoo_rpc", "test_id": "CRUD Heavy Load_zenoo_rpc_1753585708", "timestamp": "2025-07-27T10:08:28.054652", "summary": {"total_operations": 200, "success_count": 160, "success_rate": 80.0, "error_rate": 20.0, "total_duration": 3.388611, "throughput": 47.216986546995216}, "response_times": {"avg": 327.8658376898966, "median": 51.00966649479233, "std_dev": 455.34669523718634, "coefficient_of_variation": 1.3888201907387017, "percentiles": {"p50": 51.00966649479233, "p75": 1021.1127604852663, "p90": 1021.1993875098415, "p95": 1021.2438601331087, "p99": 1021.2884407024831, "p99.9": 1021.3061425365158}, "outliers_count": 0, "min": 0.0009160139597952366, "max": 1021.3079170207493}, "system_resources": {"avg_cpu_usage": 0.30606060606060603, "avg_memory_usage": 26.25094696969697, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 40, "timeouts": 0, "connection_errors": 0, "error_details": [{"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160694"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160706"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160713"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160718"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160723"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160727"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160731"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160735"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160739"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160743"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160747"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160750"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160754"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160758"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160762"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160765"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160769"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160773"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160777"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:29.160780"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307401"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307415"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307421"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307426"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307430"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307435"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307439"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307443"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307448"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307451"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307455"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307459"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307463"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307467"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307470"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307474"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307477"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307481"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307484"}, {"type": "AttributeError", "message": "'SimulatedZenooClient' object has no attribute 'env'", "traceback": "", "timestamp": "2025-07-27T10:08:30.307488"}]}, "latency_distribution": {"bins": [0.0009160139597952366, 51.06626606429927, 102.13161611463875, 153.19696616497822, 204.2623162153177, 255.32766626565717, 306.39301631599665, 357.4583663663361, 408.5237164166756, 459.5890664670151, 510.65441651735455, 561.719766567694, 612.7851166180335, 663.850466668373, 714.9158167187124, 765.9811667690519, 817.0465168193914, 868.1118668697309, 919.1772169200704, 970.2425669704098, 1021.3079170207493], "counts": [138, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60], "bin_centers": [25.533591039129533, 76.59894108946901, 127.66429113980848, 178.72964119014796, 229.79499124048743, 280.8603412908269, 331.9256913411664, 382.99104139150586, 434.05639144184534, 485.1217414921848, 536.1870915425243, 587.2524415928638, 638.3177916432032, 689.3831416935427, 740.4484917438822, 791.5138417942217, 842.5791918445611, 893.6445418949006, 944.7098919452401, 995.7752419955796]}}, "odoorpc": {"operation": "CRUD Heavy Load", "library": "odoorpc", "test_id": "CRUD Heavy Load_odoorpc_1753585711", "timestamp": "2025-07-27T10:08:31.488301", "summary": {"total_operations": 200, "success_count": 200, "success_rate": 100.0, "error_rate": 0.0, "total_duration": 143.040895, "throughput": 1.3982015422932021}, "response_times": {"avg": 715.0108404154889, "median": 140.0877085106913, "std_dev": 898.7131864816982, "coefficient_of_variation": 1.2569224628251243, "percentiles": {"p50": 140.0877085106913, "p75": 2081.5158127370523, "p90": 2085.0154830899555, "p95": 2085.0556961842813, "p99": 2085.281691394048, "p99.9": 2100.699909764342}, "outliers_count": 0, "min": 100.15604202635586, "max": 2100.8097080048174}, "system_resources": {"avg_cpu_usage": 0.29156363636363636, "avg_memory_usage": 21.699363636363636, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 0, "timeouts": 0, "connection_errors": 0, "error_details": []}, "latency_distribution": {"bins": [100.15604202635586, 200.18872532527894, 300.221408624202, 400.2540919231251, 500.28677522204816, 600.3194585209712, 700.3521418198943, 800.3848251188174, 900.4175084177405, 1000.4501917166635, 1100.4828750155866, 1200.5155583145097, 1300.5482416134328, 1400.5809249123558, 1500.613608211279, 1600.646291510202, 1700.678974809125, 1800.7116581080481, 1900.7443414069712, 2000.7770247058943, 2100.8097080048174], "counts": [140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60], "bin_centers": [150.1723836758174, 250.20506697474048, 350.23775027366355, 450.2704335725866, 550.3031168715097, 650.3358001704328, 750.3684834693559, 850.4011667682789, 950.433850067202, 1050.466533366125, 1150.4992166650482, 1250.5318999639712, 1350.5645832628943, 1450.5972665618174, 1550.6299498607405, 1650.6626331596635, 1750.6953164585866, 1850.7279997575097, 1950.7606830564328, 2050.793366355356]}}, "comparison": {"response_time_improvement_percent": 54.14533330720196, "throughput_improvement_percent": 3276.980007442578, "error_rate_improvement_percent": -20.0, "memory_improvement_percent": -20.97565352426199, "statistical_significance": {"error": "scipy not available for statistical analysis", "significant": "True"}, "performance_grade": "A"}}, "stress_concurrent_reads": {"scenario": {"name": "Stress Concurrent Reads", "description": "Maximum concurrent read operations", "iterations": 500, "concurrent_users": 50, "data_size": "medium", "complexity": "simple", "duration_seconds": 60, "ramp_up_seconds": 10, "think_time_ms": 0, "error_threshold": 5.0}, "zenoo_rpc": {"operation": "Stress Concurrent Reads", "library": "zenoo_rpc", "test_id": "Stress Concurrent Reads_zenoo_rpc_1753585854", "timestamp": "2025-07-27T10:10:54.636083", "summary": {"total_operations": 500, "success_count": 500, "success_rate": 100.0, "error_rate": 0.0, "total_duration": 1.21191, "throughput": 412.5718906519461}, "response_times": {"avg": 121.13155841373373, "median": 121.11020847805776, "std_dev": 0.08013959390763319, "coefficient_of_variation": 0.000661591371869505, "percentiles": {"p50": 121.11020847805776, "p75": 121.12987552245613, "p90": 121.16157118580304, "p95": 121.33229790488258, "p99": 121.50047256669495, "p99.9": 121.53130112343933}, "outliers_count": 43, "min": 121.06195802334696, "max": 121.5358340414241}, "system_resources": {"avg_cpu_usage": 0.75, "avg_memory_usage": 25.774739583333332, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 0, "timeouts": 0, "connection_errors": 0, "error_details": []}, "latency_distribution": {"bins": [121.06195802334696, 121.08565182425082, 121.10934562515467, 121.13303942605853, 121.15673322696239, 121.18042702786624, 121.2041208287701, 121.22781462967396, 121.25150843057781, 121.27520223148167, 121.29889603238553, 121.32258983328938, 121.34628363419324, 121.3699774350971, 121.39367123600096, 121.41736503690481, 121.44105883780867, 121.46475263871253, 121.48844643961638, 121.51214024052024, 121.5358340414241], "counts": [39, 191, 173, 43, 12, 3, 2, 3, 2, 3, 3, 2, 2, 3, 3, 3, 3, 2, 5, 3], "bin_centers": [121.07380492379889, 121.09749872470275, 121.1211925256066, 121.14488632651046, 121.16858012741432, 121.19227392831817, 121.21596772922203, 121.23966153012589, 121.26335533102974, 121.2870491319336, 121.31074293283746, 121.33443673374131, 121.35813053464517, 121.38182433554903, 121.40551813645288, 121.42921193735674, 121.4529057382606, 121.47659953916445, 121.50029334006831, 121.52398714097217]}}, "odoorpc": {"operation": "Stress Concurrent Reads", "library": "odoorpc", "test_id": "Stress Concurrent Reads_odoorpc_1753585855", "timestamp": "2025-07-27T10:10:55.879901", "summary": {"total_operations": 500, "success_count": 500, "success_rate": 100.0, "error_rate": 0.0, "total_duration": 141.996539, "throughput": 3.521212583920795}, "response_times": {"avg": 283.8867395751877, "median": 284.177624998847, "std_dev": 3.7992264381427687, "coefficient_of_variation": 0.01338289503704184, "percentiles": {"p50": 284.177624998847, "p75": 285.03317752620205, "p90": 285.0531583011616, "p95": 285.08216043992434, "p99": 286.157242541085, "p99.9": 332.58585409372046}, "outliers_count": 3, "min": 280.06645798450336, "max": 357.2137909941375}, "system_resources": {"avg_cpu_usage": 0.31979472140762466, "avg_memory_usage": 20.869570197947215, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 0, "timeouts": 0, "connection_errors": 0, "error_details": []}, "latency_distribution": {"bins": [280.06645798450336, 283.92382463498507, 287.7811912854668, 291.6385579359485, 295.4959245864302, 299.3532912369119, 303.2106578873936, 307.0680245378753, 310.925391188357, 314.78275783883873, 318.64012448932044, 322.49749113980215, 326.35485779028386, 330.21222444076557, 334.0695910912473, 337.926957741729, 341.7843243922107, 345.6416910426924, 349.4990576931741, 353.3564243436558, 357.2137909941375], "counts": [236, 261, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "bin_centers": [281.9951413097442, 285.8525079602259, 289.70987461070763, 293.56724126118934, 297.42460791167105, 301.28197456215275, 305.13934121263446, 308.99670786311617, 312.8540745135979, 316.7114411640796, 320.5688078145613, 324.426174465043, 328.2835411155247, 332.1409077660064, 335.99827441648813, 339.85564106696984, 343.71300771745155, 347.57037436793325, 351.42774101841496, 355.28510766889667]}}, "comparison": {"response_time_improvement_percent": 57.331026241311314, "throughput_improvement_percent": 11616.75611225256, "error_rate_improvement_percent": 0.0, "memory_improvement_percent": -23.50393102905685, "statistical_significance": {"error": "scipy not available for statistical analysis", "significant": "True"}, "performance_grade": "A"}}, "memory_large_datasets": {"scenario": {"name": "Memory Large Datasets", "description": "Processing large datasets to test memory efficiency", "iterations": 50, "concurrent_users": 5, "data_size": "xlarge", "complexity": "complex", "duration_seconds": null, "ramp_up_seconds": 0, "think_time_ms": 500, "error_threshold": 5.0}, "zenoo_rpc": {"operation": "Memory Large Datasets", "library": "zenoo_rpc", "test_id": "Memory Large Datasets_zenoo_rpc_1753585997", "timestamp": "2025-07-27T10:13:17.921161", "summary": {"total_operations": 50, "success_count": 50, "success_rate": 100.0, "error_rate": 0.0, "total_duration": 15.220721, "throughput": 3.284995500541663}, "response_times": {"avg": 1021.0283815802541, "median": 1021.1176665034145, "std_dev": 0.2543651620035976, "coefficient_of_variation": 0.0002491264362406014, "percentiles": {"p50": 1021.1176665034145, "p75": 1021.1332915059756, "p90": 1021.1486208776478, "p95": 1021.2032643990824, "p99": 1021.2332346715266, "p99.9": 1021.2399229058647}, "outliers_count": 14, "min": 1020.2835419913754, "max": 1021.2406660430133}, "system_resources": {"avg_cpu_usage": 0.3952054794520548, "avg_memory_usage": 23.27750428082192, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 0, "timeouts": 0, "connection_errors": 0, "error_details": []}, "latency_distribution": {"bins": [1020.2835419913754, 1020.3313981939573, 1020.3792543965392, 1020.4271105991211, 1020.474966801703, 1020.5228230042849, 1020.5706792068668, 1020.6185354094487, 1020.6663916120306, 1020.7142478146125, 1020.7621040171944, 1020.8099602197763, 1020.8578164223582, 1020.90567262494, 1020.953528827522, 1021.0013850301038, 1021.0492412326857, 1021.0970974352676, 1021.1449536378495, 1021.1928098404314, 1021.2406660430133], "counts": [5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 0, 0, 1, 33, 2, 4], "bin_centers": [1020.3074700926663, 1020.3553262952482, 1020.4031824978301, 1020.451038700412, 1020.4988949029939, 1020.5467511055758, 1020.5946073081577, 1020.6424635107396, 1020.6903197133215, 1020.7381759159034, 1020.7860321184853, 1020.8338883210672, 1020.8817445236491, 1020.929600726231, 1020.9774569288129, 1021.0253131313948, 1021.0731693339767, 1021.1210255365586, 1021.1688817391405, 1021.2167379417224]}}, "odoorpc": {"operation": "Memory Large Datasets", "library": "odoorpc", "test_id": "Memory Large Datasets_odoorpc_1753586013", "timestamp": "2025-07-27T10:13:33.206234", "summary": {"total_operations": 50, "success_count": 50, "success_rate": 100.0, "error_rate": 0.0, "total_duration": 104.711944, "throughput": 0.47750044636741723}, "response_times": {"avg": 2083.7443691759836, "median": 2084.146542008966, "std_dev": 1.3815609954261032, "coefficient_of_variation": 0.0006630184661146517, "percentiles": {"p50": 2084.146542008966, "p75": 2085.027083478053, "p90": 2085.048757988261, "p95": 2085.0537921098294, "p99": 2085.0750405120198, "p99.9": 2085.0841912546894}, "outliers_count": 0, "min": 2080.505375051871, "max": 2085.085208003875}, "system_resources": {"avg_cpu_usage": 0.30737051792828685, "avg_memory_usage": 19.14148095119522, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 0, "timeouts": 0, "connection_errors": 0, "error_details": []}, "latency_distribution": {"bins": [2080.505375051871, 2080.734366699471, 2080.9633583470713, 2081.1923499946715, 2081.4213416422717, 2081.650333289872, 2081.879324937472, 2082.1083165850723, 2082.3373082326725, 2082.5662998802727, 2082.795291527873, 2083.024283175473, 2083.2532748230733, 2083.4822664706735, 2083.7112581182737, 2083.940249765874, 2084.169241413474, 2084.3982330610743, 2084.6272247086745, 2084.8562163562747, 2085.085208003875], "counts": [1, 2, 0, 1, 1, 2, 0, 3, 1, 3, 1, 1, 3, 3, 0, 3, 3, 2, 1, 19], "bin_centers": [2080.619870875671, 2080.8488625232712, 2081.0778541708714, 2081.3068458184716, 2081.535837466072, 2081.764829113672, 2081.993820761272, 2082.2228124088724, 2082.4518040564726, 2082.680795704073, 2082.909787351673, 2083.138778999273, 2083.3677706468734, 2083.5967622944736, 2083.825753942074, 2084.054745589674, 2084.283737237274, 2084.5127288848744, 2084.7417205324746, 2084.9707121800748]}}, "comparison": {"response_time_improvement_percent": 51.00030518695441, "throughput_improvement_percent": 587.9565297859413, "error_rate_improvement_percent": 0.0, "memory_improvement_percent": -21.60764540722979, "statistical_significance": {"error": "scipy not available for statistical analysis", "significant": "True"}, "performance_grade": "A"}}, "workflow_sales_process": {"scenario": {"name": "Sales Process Workflow", "description": "Complete sales order workflow simulation", "iterations": 100, "concurrent_users": 8, "data_size": "medium", "complexity": "complex", "duration_seconds": null, "ramp_up_seconds": 0, "think_time_ms": 300, "error_threshold": 5.0}, "zenoo_rpc": {"operation": "Sales Process Workflow", "library": "zenoo_rpc", "test_id": "Sales Process Workflow_zenoo_rpc_1753586118", "timestamp": "2025-07-27T10:15:18.013249", "summary": {"total_operations": 96, "success_count": 96, "success_rate": 100.0, "error_rate": 0.0, "total_duration": 4.215767, "throughput": 22.771656972503465}, "response_times": {"avg": 50.13681437534009, "median": 50.07485451642424, "std_dev": 0.7075966933643103, "coefficient_of_variation": 0.01411331577764429, "percentiles": {"p50": 50.07485451642424, "p75": 50.12557326699607, "p90": 50.35450047580525, "p95": 52.260395998018794, "p99": 52.26190903049428, "p99.9": 52.26746652304428}, "outliers_count": 16, "min": 49.141791998408735, "max": 52.2680840222165}, "system_resources": {"avg_cpu_usage": 0.34146341463414637, "avg_memory_usage": 23.493140243902438, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 0, "timeouts": 0, "connection_errors": 0, "error_details": []}, "latency_distribution": {"bins": [49.141791998408735, 49.29810659959912, 49.45442120078951, 49.6107358019799, 49.76705040317029, 49.923365004360676, 50.079679605551064, 50.23599420674145, 50.39230880793184, 50.54862340912223, 50.70493801031262, 50.861252611503005, 51.01756721269339, 51.17388181388378, 51.33019641507417, 51.48651101626456, 51.642825617454946, 51.799140218645334, 51.95545481983572, 52.11176942102611, 52.2680840222165], "counts": [8, 0, 0, 8, 8, 29, 27, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], "bin_centers": [49.21994929900393, 49.37626390019432, 49.532578501384705, 49.68889310257509, 49.84520770376548, 50.00152230495587, 50.15783690614626, 50.314151507336646, 50.470466108527035, 50.62678070971742, 50.78309531090781, 50.9394099120982, 51.09572451328859, 51.252039114478976, 51.408353715669364, 51.56466831685975, 51.72098291805014, 51.87729751924053, 52.03361212043092, 52.189926721621305]}}, "odoorpc": {"operation": "Sales Process Workflow", "library": "odoorpc", "test_id": "Sales Process Workflow_odoorpc_1753586122", "timestamp": "2025-07-27T10:15:22.296295", "summary": {"total_operations": 96, "success_count": 96, "success_rate": 100.0, "error_rate": 0.0, "total_duration": 17.923544, "throughput": 5.356083595967405}, "response_times": {"avg": 183.5028880407966, "median": 183.21193795418367, "std_dev": 2.319989632407599, "coefficient_of_variation": 0.01264279629153202, "percentiles": {"p50": 183.21193795418367, "p75": 184.92859324032906, "p90": 186.0171665030066, "p95": 186.07310448714998, "p99": 190.90592952561565, "p99.9": 193.13341826607942}, "outliers_count": 2, "min": 178.24041697895154, "max": 193.3809170150198}, "system_resources": {"avg_cpu_usage": 0.39476744186046514, "avg_memory_usage": 23.379905523255815, "memory_peak": 0.0, "memory_growth": 0.0}, "network": {"total_bytes_transferred": 0, "avg_request_size": 0.0, "avg_response_size": 0.0}, "errors": {"total_errors": 0, "timeouts": 0, "connection_errors": 0, "error_details": []}, "latency_distribution": {"bins": [178.24041697895154, 178.99744198075496, 179.75446698255837, 180.51149198436178, 181.2685169861652, 182.0255419879686, 182.78256698977202, 183.53959199157543, 184.29661699337885, 185.05364199518226, 185.81066699698567, 186.5676919987891, 187.3247170005925, 188.0817420023959, 188.83876700419933, 189.59579200600274, 190.35281700780615, 191.10984200960957, 191.86686701141298, 192.6238920132164, 193.3809170150198], "counts": [4, 1, 0, 3, 14, 19, 14, 10, 7, 7, 13, 1, 1, 0, 0, 0, 1, 0, 0, 1], "bin_centers": [178.61892947985325, 179.37595448165666, 180.13297948346008, 180.8900044852635, 181.6470294870669, 182.40405448887032, 183.16107949067373, 183.91810449247714, 184.67512949428055, 185.43215449608397, 186.18917949788738, 186.9462044996908, 187.7032295014942, 188.46025450329762, 189.21727950510103, 189.97430450690445, 190.73132950870786, 191.48835451051127, 192.24537951231468, 193.0024045141181]}}, "comparison": {"response_time_improvement_percent": 72.67791536654529, "throughput_improvement_percent": 325.1549955203881, "error_rate_improvement_percent": 0.0, "memory_improvement_percent": -0.48432497100550653, "statistical_significance": {"error": "scipy not available for statistical analysis", "significant": "True"}, "performance_grade": "A"}}}, "summary": {"overall_improvement": 63.359458462253876, "best_improvement": 76.32153690526286, "best_scenario": "crud_light_load", "avg_throughput_improvement": 2721.990077898724, "scenarios_count": 6}, "recommendations": ["🚀 Immediate migration to zenoo_rpc recommended - significant performance gains demonstrated", "🔧 Enable intelligent caching for frequently accessed data", "📊 Implement batch operations for bulk data processing", "🔄 Use connection pooling for high-concurrency scenarios", "📈 Set up performance monitoring in production", "🛡️ Implement circuit breaker patterns for resilience", "⚙️ Optimize async/await usage for maximum performance benefits", "📋 Conduct load testing before production deployment", "🔍 Monitor memory usage patterns and optimize as needed"]}