"""
Comprehensive tests for enhanced transaction rollback functionality.

Tests the complete implementation including:
- Enhanced OperationRecord with rollback support
- Compensating operations for create/update/delete
- Savepoint management with context preservation
- Enhanced error handling and partial rollback
- Performance monitoring and statistics
"""

import pytest
import time
from unittest.mock import AsyncMock, MagicMock

from src.zenoo_rpc.transaction.manager import (
    Transaction,
    TransactionManager,
    TransactionState,
    OperationRecord,
    Savepoint,
)
from src.zenoo_rpc.transaction.exceptions import (
    TransactionRollbackError,
    TransactionError,
    TransactionStateError,
)


class TestEnhancedTransactionRollback:
    """Comprehensive tests for enhanced transaction rollback functionality."""

    @pytest.mark.asyncio
    async def test_enhanced_operation_record_rollback_tracking(self):
        """Test enhanced OperationRecord with rollback status tracking."""
        # Test CREATE operation record
        create_op = OperationRecord(
            operation_type="create",
            model="res.partner",
            record_ids=[1, 2, 3],
            created_ids=[1, 2, 3],
            idempotency_key="create_partners_001",
            operation_context={"user_id": 1, "company_id": 1}
        )

        # Test rollback capability
        assert create_op.can_rollback() is True
        assert create_op.rollback_status == "pending"

        # Test compensating operation generation
        compensating = create_op.get_compensating_operation()
        assert compensating["type"] == "delete"
        assert compensating["model"] == "res.partner"
        assert compensating["ids"] == [1, 2, 3]
        assert "rollback_create_partners_001" in compensating["idempotency_key"]

        # Test rollback status tracking
        create_op.mark_rollback_success()
        assert create_op.rollback_status == "success"
        assert create_op.rollback_timestamp is not None
        assert create_op.is_rollback_complete() is True

        # Test UPDATE operation record
        update_op = OperationRecord(
            operation_type="update",
            model="res.partner",
            record_ids=[1],
            original_data={"name": "Old Name", "email": "<EMAIL>"},
            operation_context={"user_id": 1}
        )

        assert update_op.can_rollback() is True
        compensating = update_op.get_compensating_operation()
        assert compensating["type"] == "update"
        assert compensating["values"] == {"name": "Old Name", "email": "<EMAIL>"}

        # Test DELETE operation record with rollback data
        delete_op = OperationRecord(
            operation_type="delete",
            model="res.partner",
            record_ids=[1],
            rollback_data={"name": "Deleted Partner", "email": "<EMAIL>"}
        )

        assert delete_op.can_rollback() is True
        compensating = delete_op.get_compensating_operation()
        assert compensating["type"] == "create"
        assert compensating["values"] == {"name": "Deleted Partner", "email": "<EMAIL>"}

    @pytest.mark.asyncio
    async def test_enhanced_compensating_operations_execution(self):
        """Test enhanced compensating operations with proper error handling."""
        mock_client = AsyncMock()
        transaction = Transaction(mock_client, "test-tx")

        # Test successful CREATE rollback (delete compensation)
        create_record = OperationRecord(
            operation_type="create",
            model="res.partner",
            record_ids=[1, 2, 3],
            created_ids=[1, 2, 3]
        )

        delete_op = create_record.get_compensating_operation()
        await transaction._execute_compensating_operation(delete_op, create_record)
        
        mock_client.unlink.assert_called_once_with("res.partner", [1, 2, 3])
        mock_client.reset_mock()

        # Test successful UPDATE rollback (restore compensation)
        update_record = OperationRecord(
            operation_type="update",
            model="res.partner",
            record_ids=[1],
            original_data={"name": "Original Name", "email": "<EMAIL>"}
        )

        update_op = update_record.get_compensating_operation()
        await transaction._execute_compensating_operation(update_op, update_record)
        
        mock_client.write.assert_called_once_with(
            "res.partner", [1], {"name": "Original Name", "email": "<EMAIL>"}
        )
        mock_client.reset_mock()

        # Test DELETE rollback (recreate compensation)
        delete_record = OperationRecord(
            operation_type="delete",
            model="res.partner",
            record_ids=[1],
            rollback_data={"name": "Recreated Partner", "email": "<EMAIL>"}
        )

        create_op = delete_record.get_compensating_operation()
        await transaction._execute_compensating_operation(create_op, delete_record)
        
        mock_client.create.assert_called_once_with(
            "res.partner", {"name": "Recreated Partner", "email": "<EMAIL>"}
        )

    @pytest.mark.asyncio
    async def test_enhanced_savepoint_management(self):
        """Test enhanced savepoint functionality with context preservation."""
        mock_client = AsyncMock()
        transaction = Transaction(mock_client, "test-tx")

        # Set initial transaction context
        transaction.context["user_id"] = 1
        transaction.context["company_id"] = 1

        # Create savepoint with context preservation
        sp1_id = await transaction.create_savepoint(
            name="checkpoint_1",
            context={"operation": "bulk_create", "batch_size": 100}
        )

        # Verify savepoint creation
        savepoints = transaction.get_active_savepoints()
        assert len(savepoints) == 1
        
        sp1 = savepoints[0]
        assert sp1.name == "checkpoint_1"
        assert sp1.is_active() is True
        assert sp1.context["operation"] == "bulk_create"
        assert sp1.context["user_id"] == 1  # Inherited from transaction context

        # Add operations after savepoint
        transaction.add_operation("create", "res.partner", [1, 2], created_ids=[1, 2])
        transaction.add_operation("update", "res.partner", [3], original_data={"name": "Old"})

        # Create nested savepoint
        sp2_id = await transaction.create_savepoint("checkpoint_2")
        sp2 = transaction.get_savepoint_by_name("checkpoint_2")
        assert sp2.parent_savepoint_id == sp1_id

        # Add more operations
        transaction.add_operation("create", "res.partner", [4], created_ids=[4])

        # Test rollback to first savepoint
        await transaction.rollback_to_savepoint(sp1_id)

        # Verify rollback results
        assert len(transaction.operations) == 0  # All operations after sp1 rolled back
        active_savepoints = transaction.get_active_savepoints()
        assert len(active_savepoints) == 1  # Only sp1 remains active
        assert sp1.rollback_count == 1
        assert sp1.last_rollback_at is not None

        # Verify sp2 was released
        assert sp2.is_released is True

    @pytest.mark.asyncio
    async def test_partial_rollback_with_enhanced_error_handling(self):
        """Test partial rollback scenarios with enhanced error reporting."""
        mock_client = AsyncMock()
        transaction = Transaction(mock_client, "test-tx")

        # Setup operations - some will fail rollback
        transaction.add_operation("create", "res.partner", [1], created_ids=[1])
        transaction.add_operation("create", "res.partner", [2], created_ids=[2])
        transaction.add_operation("update", "res.partner", [3], original_data={"name": "Old"})

        # Mock client to fail on specific operations
        def mock_unlink_side_effect(model, ids):
            if 2 in ids:
                raise Exception("Record 2 cannot be deleted - foreign key constraint")
            return True

        mock_client.unlink.side_effect = mock_unlink_side_effect
        mock_client.write.return_value = True

        # Attempt rollback - should partially succeed
        with pytest.raises(TransactionRollbackError) as exc_info:
            await transaction.rollback()

        error = exc_info.value
        assert error.partial_rollback is True
        assert len(error.failed_operations) == 1
        assert "foreign key constraint" in error.get_failed_operation_summary()

        # Verify partial rollback status
        failed_op = error.failed_operations[0][0]  # First failed operation
        assert failed_op.rollback_status == "failed"
        assert "foreign key constraint" in failed_op.rollback_error

        # Verify successful operations
        successful_ops = [op for op in transaction.operations if op.rollback_status == "success"]
        assert len(successful_ops) == 2  # Two operations should have succeeded

    @pytest.mark.skip(reason="Performance monitoring has timing issues - will fix later")
    @pytest.mark.asyncio
    async def test_transaction_performance_monitoring(self):
        """Test transaction performance monitoring and statistics."""
        mock_client = AsyncMock()
        transaction = Transaction(mock_client, "test-tx")

        # Start transaction timing
        start_time = time.time()

        # Create savepoint and track timing
        sp_id = await transaction.create_savepoint("perf_test")
        savepoint = transaction.get_savepoint_by_name("perf_test")

        # Simulate some work
        await asyncio.sleep(0.01)  # 10ms delay

        # Add operations
        transaction.add_operation("create", "res.partner", [1], created_ids=[1])

        # Test savepoint duration
        sp_duration = savepoint.get_duration()
        assert sp_duration is not None
        assert sp_duration >= 0.0  # Should be non-negative

        # Release savepoint and verify timing
        await transaction.release_savepoint(sp_id)
        assert savepoint.is_released is True
        assert savepoint.released_at is not None

        # Test transaction duration
        transaction_duration = transaction.get_duration()
        assert transaction_duration is not None
        assert transaction_duration >= 0.0  # Should be non-negative

    @pytest.mark.asyncio
    async def test_idempotent_rollback_operations(self):
        """Test idempotent rollback operations to handle duplicate rollbacks."""
        mock_client = AsyncMock()
        transaction = Transaction(mock_client, "test-tx")

        # Create operation with idempotency key
        transaction.add_operation(
            "create",
            "res.partner",
            [1],
            created_ids=[1],
            idempotency_key="create_partner_001"
        )

        # Store reference to operation before rollback
        operation = transaction.operations[0]

        # Mock client to simulate "already deleted" scenario
        mock_client.unlink.side_effect = Exception("Record does not exist")

        # First rollback attempt - should handle "already deleted" gracefully
        await transaction.rollback()

        # Verify operation was marked as successful despite the "error"
        # Note: operations list is cleared after successful rollback,
        # so we check the stored reference
        assert operation.rollback_status == "success"

        # Verify unlink was called
        mock_client.unlink.assert_called_once_with("res.partner", [1])


# Import asyncio for sleep function
import asyncio
