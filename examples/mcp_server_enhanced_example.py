#!/usr/bin/env python3
"""
Enhanced MCP Server Example - Leveraging Zenoo RPC Features

This example demonstrates how MCP server SHOULD leverage all the powerful
features in Zenoo RPC codebase instead of using mock implementations.

Features showcased:
- OdooModel system with type safety
- QueryBuilder with fluent interface
- Transaction management
- Caching system
- Model registry
- Relationship management
- Exception handling
"""

import asyncio
import logging
import sys
import os
from typing import Dict, Any, List

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from zenoo_rpc import ZenooClient
from zenoo_rpc.models.common import ResPartner, SaleOrder, SaleOrderLine
from zenoo_rpc.query import Q, Field
from zenoo_rpc.mcp_server import ZenooMCPServer, MCPServerConfig

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedMCPServer(ZenooMCPServer):
    """Enhanced MCP Server that properly leverages Zenoo RPC features."""
    
    async def enhanced_search_records(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced search using QueryBuilder and OdooModel system."""
        model_name = args["model"]
        domain = args.get("domain", [])
        limit = args.get("limit", 100)
        
        try:
            if model_name == "res.partner":
                # Use type-safe OdooModel with QueryBuilder
                query = self.zenoo_client.model(ResPartner)
                
                # Apply filters using fluent interface
                if domain:
                    for item in domain:
                        if item == ["is_company", "=", True]:
                            query = query.filter(is_company=True)
                        elif item[0] == "name" and item[1] == "ilike":
                            query = query.filter(name__ilike=item[2])
                
                # Execute with caching
                partners = await query.limit(limit).all()
                
                # Return type-safe data
                return {
                    "records": [partner.to_dict() for partner in partners],
                    "count": len(partners),
                    "model": model_name,
                    "type_safe": True
                }
                
            elif model_name == "sale.order":
                # Use SaleOrder model with relationships
                query = self.zenoo_client.model(SaleOrder)
                
                # Complex query with relationships
                orders = await query.filter(
                    state__in=["sale", "done"]
                ).order_by("-date_order").limit(limit).all()
                
                # Include relationship data
                order_data = []
                for order in orders:
                    order_dict = order.to_dict()
                    # Access partner relationship
                    if hasattr(order, 'partner_id'):
                        partner = await order.partner_id.get()
                        order_dict["partner_name"] = partner.name if partner else None
                    order_data.append(order_dict)
                
                return {
                    "records": order_data,
                    "count": len(order_data),
                    "model": model_name,
                    "with_relationships": True
                }
            
            else:
                # Fallback to dynamic model access
                records = await self.zenoo_client.search_read(
                    model_name, domain, limit=limit
                )
                return {
                    "records": records,
                    "count": len(records),
                    "model": model_name,
                    "dynamic": True
                }
                
        except Exception as e:
            logger.error(f"Enhanced search failed: {e}")
            raise
    
    async def enhanced_create_with_transaction(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Create records with transaction management and validation."""
        model_name = args["model"]
        values = args["values"]
        
        try:
            # Use transaction for ACID compliance
            async with self.zenoo_client.transaction() as tx:
                if model_name == "res.partner":
                    # Type-safe creation with validation
                    partner = await tx.create(ResPartner, values)
                    
                    # Additional business logic
                    if partner.is_company and not partner.email:
                        logger.warning(f"Company {partner.name} created without email")
                    
                    return {
                        "record": partner.to_dict(),
                        "model": model_name,
                        "transaction_id": tx.transaction_id,
                        "type_safe": True
                    }
                
                elif model_name == "sale.order":
                    # Complex creation with relationships
                    order = await tx.create(SaleOrder, values)
                    
                    # Create order lines if provided
                    if "order_lines" in values:
                        for line_data in values["order_lines"]:
                            line_data["order_id"] = order.id
                            await tx.create(SaleOrderLine, line_data)
                    
                    return {
                        "record": order.to_dict(),
                        "model": model_name,
                        "transaction_id": tx.transaction_id,
                        "with_lines": "order_lines" in values
                    }
                
                else:
                    # Fallback creation
                    record_id = await self.zenoo_client.create(model_name, values)
                    return {
                        "id": record_id,
                        "values": values,
                        "model": model_name,
                        "transaction_id": tx.transaction_id
                    }
                    
        except Exception as e:
            logger.error(f"Enhanced create failed: {e}")
            raise
    
    async def enhanced_complex_query(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Demonstrate complex queries using Q objects and Field expressions."""
        try:
            # Complex query using Q objects
            complex_filter = (
                Q(is_company=True) & 
                (Q(name__ilike="tech%") | Q(name__ilike="soft%"))
            )
            
            # Use Field expressions for advanced operations
            partners = await self.zenoo_client.model(ResPartner).filter(
                complex_filter
            ).annotate(
                name_length=Field("name").length()
            ).order_by("-name_length").limit(10).all()
            
            return {
                "records": [p.to_dict() for p in partners],
                "query_type": "complex_q_objects",
                "filter_description": "Companies with names starting with 'tech' or 'soft'",
                "annotated_fields": ["name_length"]
            }
            
        except Exception as e:
            logger.error(f"Complex query failed: {e}")
            raise
    
    async def enhanced_batch_operations(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Demonstrate batch operations for performance."""
        try:
            # Batch update using QueryBuilder
            partner_ids = args.get("partner_ids", [])
            update_values = args.get("values", {})
            
            # Efficient batch update
            updated_count = await self.zenoo_client.model(ResPartner).filter(
                id__in=partner_ids
            ).update(update_values)
            
            return {
                "updated_count": updated_count,
                "partner_ids": partner_ids,
                "values": update_values,
                "operation": "batch_update"
            }
            
        except Exception as e:
            logger.error(f"Batch operation failed: {e}")
            raise
    
    async def enhanced_analytics_query(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Advanced analytics using aggregation and grouping."""
        try:
            from datetime import datetime, timedelta
            
            # Analytics query for sales data
            start_date = datetime.now() - timedelta(days=30)
            
            # Group by partner and calculate totals
            sales_analytics = await self.zenoo_client.model(SaleOrder).filter(
                date_order__gte=start_date,
                state__in=["sale", "done"]
            ).group_by("partner_id").aggregate(
                total_amount=Field("amount_total").sum(),
                order_count=Field("id").count(),
                avg_amount=Field("amount_total").avg()
            ).order_by("-total_amount").limit(20).all()
            
            return {
                "analytics": sales_analytics,
                "period": "last_30_days",
                "metrics": ["total_amount", "order_count", "avg_amount"],
                "grouped_by": "partner_id"
            }
            
        except Exception as e:
            logger.error(f"Analytics query failed: {e}")
            raise


async def demo_enhanced_mcp_server():
    """Demonstrate enhanced MCP server with Zenoo RPC features."""
    print("\n🚀 Enhanced MCP Server Demo - Leveraging Zenoo RPC Features")
    print("=" * 70)
    
    try:
        # Create enhanced server
        config = MCPServerConfig(
            name="enhanced-odoo-server",
            description="Enhanced MCP Server leveraging all Zenoo RPC features"
        )
        
        # Mock Zenoo client for demo
        class MockZenooClient:
            async def transaction(self):
                class MockTransaction:
                    transaction_id = "tx_123"
                    async def __aenter__(self):
                        return self
                    async def __aexit__(self, *args):
                        pass
                    async def create(self, model_class, values):
                        return model_class(id=123, **values)
                return MockTransaction()
            
            def model(self, model_class):
                class MockQuery:
                    def filter(self, **kwargs):
                        return self
                    def limit(self, n):
                        return self
                    async def all(self):
                        return [model_class(id=1, name="Test Record")]
                return MockQuery()
        
        server = EnhancedMCPServer(config)
        server.zenoo_client = MockZenooClient()
        
        print("✅ Enhanced server created with Zenoo RPC integration")
        
        # Test enhanced search
        search_result = await server.enhanced_search_records({
            "model": "res.partner",
            "domain": [["is_company", "=", True]],
            "limit": 10
        })
        print(f"✅ Enhanced search: {search_result['count']} records, type_safe: {search_result.get('type_safe')}")
        
        # Test transaction-based creation
        create_result = await server.enhanced_create_with_transaction({
            "model": "res.partner",
            "values": {"name": "Test Company", "is_company": True}
        })
        print(f"✅ Transaction create: ID {create_result['record']['id']}, TX: {create_result.get('transaction_id')}")
        
        print("\n🎯 Key Zenoo RPC Features Leveraged:")
        print("  🔧 OdooModel system with type safety")
        print("  📊 QueryBuilder with fluent interface")
        print("  🔄 Transaction management with ACID compliance")
        print("  ⚡ Relationship management and lazy loading")
        print("  📈 Complex queries with Q objects and Field expressions")
        print("  🚀 Batch operations for performance")
        print("  📊 Analytics and aggregation capabilities")
        
        print("\n✅ Enhanced MCP server demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise


async def main():
    """Run enhanced MCP server demonstration."""
    await demo_enhanced_mcp_server()


if __name__ == "__main__":
    asyncio.run(main())
