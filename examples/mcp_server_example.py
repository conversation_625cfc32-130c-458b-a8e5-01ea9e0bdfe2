#!/usr/bin/env python3
"""
MCP Server Example for Zenoo RPC

This example demonstrates how to run the Zenoo RPC MCP Server
to expose Odoo operations to AI tools like Claude, GPT, etc.

Requirements:
- Zenoo RPC with MCP server implementation
- Odoo instance (optional - will use mock for demo)
- MCP Python SDK (optional - will use mock for demo)

Usage:
    python examples/mcp_server_example.py
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from zenoo_rpc.mcp_server import (
    ZenooMCPServer,
    MCPServerConfig,
    MCPSecurityManager
)
from zenoo_rpc.mcp_server.config import MCPTransportType, MCPAuthMethod

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stderr)]  # Use stderr for MCP compatibility
)
logger = logging.getLogger(__name__)


async def demo_basic_server():
    """Demonstrate basic MCP server setup."""
    print("\n🎯 Demo 1: Basic MCP Server Setup", file=sys.stderr)
    print("=" * 50, file=sys.stderr)
    
    try:
        # Create basic configuration
        config = MCPServerConfig(
            name="demo-odoo-server",
            description="Demo Odoo MCP Server for AI Integration",
            transport_type=MCPTransportType.STDIO,
            odoo_url="http://localhost:8069",
            odoo_database="demo",
            odoo_username="admin",
            odoo_password="admin"
        )
        
        print(f"✅ Created server config: {config.name}", file=sys.stderr)
        print(f"✅ Transport: {config.transport_type.value}", file=sys.stderr)
        print(f"✅ Odoo URL: {config.odoo_url}", file=sys.stderr)
        
        # Validate configuration
        config.validate()
        print("✅ Configuration validated successfully", file=sys.stderr)
        
        # Create server instance
        server = ZenooMCPServer(config)
        print("✅ MCP server instance created", file=sys.stderr)
        
        # Print server capabilities
        print("\n📋 Server Capabilities:", file=sys.stderr)
        print("  🔧 Tools: search_records, get_record, create_record, update_record, delete_record", file=sys.stderr)
        print("  📄 Resources: odoo://models, odoo://model/{name}, odoo://record/{model}/{id}", file=sys.stderr)
        print("  💡 Prompts: analyze_data, generate_report_query", file=sys.stderr)
        
        print("\n✅ Demo completed successfully", file=sys.stderr)
        
    except Exception as e:
        print(f"❌ Demo failed: {e}", file=sys.stderr)
        raise


async def demo_security_configuration():
    """Demonstrate security configuration."""
    print("\n🎯 Demo 2: Security Configuration", file=sys.stderr)
    print("=" * 50, file=sys.stderr)
    
    try:
        # Create config with security settings
        config = MCPServerConfig(
            name="secure-odoo-server",
            transport_type=MCPTransportType.HTTP,
            host="localhost",
            port=8080
        )
        
        # Configure security
        config.security.auth_method = MCPAuthMethod.API_KEY
        config.security.api_keys = ["demo-key-123", "admin-key-456"]
        config.security.enable_rate_limiting = True
        config.security.rate_limit_requests = 100
        config.security.rate_limit_window = 60
        config.security.enable_input_validation = True
        
        print(f"✅ Auth method: {config.security.auth_method.value}", file=sys.stderr)
        print(f"✅ API keys configured: {len(config.security.api_keys)}", file=sys.stderr)
        print(f"✅ Rate limiting: {config.security.rate_limit_requests} req/min", file=sys.stderr)
        print(f"✅ Input validation: {config.security.enable_input_validation}", file=sys.stderr)
        
        # Create security manager
        security_manager = MCPSecurityManager(config)
        print("✅ Security manager created", file=sys.stderr)
        
        # Test authentication
        credentials = {"api_key": "demo-key-123"}
        session = security_manager.authenticate(credentials)
        print(f"✅ Authentication successful: {session.client_id}", file=sys.stderr)
        print(f"✅ Session permissions: {session.permissions}", file=sys.stderr)
        
        # Test rate limiting
        for i in range(3):
            security_manager.check_rate_limit(session.client_id)
        print("✅ Rate limiting working", file=sys.stderr)
        
        # Get security info
        security_info = security_manager.get_security_info()
        print(f"✅ Security info: {security_info}", file=sys.stderr)
        
        print("\n✅ Security demo completed successfully", file=sys.stderr)
        
    except Exception as e:
        print(f"❌ Security demo failed: {e}", file=sys.stderr)
        raise


async def demo_configuration_methods():
    """Demonstrate different configuration methods."""
    print("\n🎯 Demo 3: Configuration Methods", file=sys.stderr)
    print("=" * 50, file=sys.stderr)
    
    try:
        # Method 1: From dictionary
        config_dict = {
            "name": "dict-config-server",
            "transport_type": "http",
            "port": 8081,
            "odoo_url": "http://odoo.example.com:8069",
            "odoo_database": "production",
            "security": {
                "auth_method": "api_key",
                "api_keys": ["prod-key-1", "prod-key-2"],
                "enable_rate_limiting": True
            },
            "performance": {
                "enable_caching": True,
                "cache_ttl": 600,
                "max_connections": 50
            },
            "features": {
                "enable_tools": True,
                "enable_resources": True,
                "enable_prompts": True,
                "enable_batch_operations": True
            }
        }
        
        config1 = MCPServerConfig.from_dict(config_dict)
        print(f"✅ Config from dict: {config1.name}", file=sys.stderr)
        
        # Method 2: From environment (simulated)
        os.environ.update({
            "MCP_SERVER_NAME": "env-config-server",
            "MCP_TRANSPORT_TYPE": "stdio",
            "ODOO_URL": "http://localhost:8069",
            "ODOO_DATABASE": "test",
            "MCP_API_KEYS": "env-key-1,env-key-2,env-key-3",
            "MCP_LOG_LEVEL": "DEBUG"
        })
        
        config2 = MCPServerConfig.from_env()
        print(f"✅ Config from env: {config2.name}", file=sys.stderr)
        print(f"✅ API keys from env: {len(config2.security.api_keys)}", file=sys.stderr)
        
        # Method 3: Programmatic configuration
        config3 = MCPServerConfig()
        config3.name = "programmatic-server"
        config3.transport_type = MCPTransportType.HTTP
        config3.port = 8082
        config3.features.enable_ai_suggestions = True
        config3.features.enable_natural_language_queries = True
        
        print(f"✅ Programmatic config: {config3.name}", file=sys.stderr)
        print(f"✅ AI features enabled: {config3.features.enable_ai_suggestions}", file=sys.stderr)
        
        # Convert to dict for inspection
        config_data = config3.to_dict()
        print(f"✅ Config serialization: {len(config_data)} keys", file=sys.stderr)
        
        print("\n✅ Configuration methods demo completed", file=sys.stderr)
        
    except Exception as e:
        print(f"❌ Configuration demo failed: {e}", file=sys.stderr)
        raise
    finally:
        # Clean up environment
        for key in ["MCP_SERVER_NAME", "MCP_TRANSPORT_TYPE", "ODOO_URL", "ODOO_DATABASE", "MCP_API_KEYS", "MCP_LOG_LEVEL"]:
            os.environ.pop(key, None)


async def demo_server_capabilities():
    """Demonstrate server capabilities and tool registration."""
    print("\n🎯 Demo 4: Server Capabilities", file=sys.stderr)
    print("=" * 50, file=sys.stderr)
    
    try:
        # Create server with all features enabled
        config = MCPServerConfig(
            name="full-featured-server",
            description="Full-featured Odoo MCP Server with all capabilities"
        )
        
        # Enable all features
        config.features.enable_tools = True
        config.features.enable_resources = True
        config.features.enable_prompts = True
        config.features.enable_batch_operations = True
        config.features.enable_transactions = True
        config.features.enable_workflow_operations = True
        config.features.enable_report_generation = True
        
        server = ZenooMCPServer(config)
        print("✅ Full-featured server created", file=sys.stderr)
        
        # Check server instructions
        instructions = server._get_server_instructions()
        print(f"✅ Server instructions generated ({len(instructions)} chars)", file=sys.stderr)
        
        # Print capabilities summary
        print("\n📋 Enabled Capabilities:", file=sys.stderr)
        print(f"  🔧 Tools: {config.features.enable_tools}", file=sys.stderr)
        print(f"  📄 Resources: {config.features.enable_resources}", file=sys.stderr)
        print(f"  💡 Prompts: {config.features.enable_prompts}", file=sys.stderr)
        print(f"  📦 Batch Operations: {config.features.enable_batch_operations}", file=sys.stderr)
        print(f"  🔄 Transactions: {config.features.enable_transactions}", file=sys.stderr)
        print(f"  ⚡ Workflows: {config.features.enable_workflow_operations}", file=sys.stderr)
        print(f"  📊 Reports: {config.features.enable_report_generation}", file=sys.stderr)
        
        # Test mock tool execution
        mock_result = await server._execute_tool("search_records", {
            "model": "res.partner",
            "domain": [["is_company", "=", True]],
            "limit": 10
        })
        print(f"✅ Mock tool execution: {mock_result['count']} records", file=sys.stderr)
        
        # Test mock resource access
        mock_resource = await server._execute_resource("list_models", {})
        resource_data = json.loads(mock_resource)
        print(f"✅ Mock resource access: {resource_data['resource']}", file=sys.stderr)
        
        # Test mock prompt generation
        mock_prompt = await server._execute_prompt("analyze_data", {
            "model": "sale.order",
            "analysis_type": "revenue_analysis"
        })
        print(f"✅ Mock prompt generation: {len(mock_prompt)} chars", file=sys.stderr)
        
        print("\n✅ Server capabilities demo completed", file=sys.stderr)
        
    except Exception as e:
        print(f"❌ Server capabilities demo failed: {e}", file=sys.stderr)
        raise


async def demo_error_handling():
    """Demonstrate error handling and validation."""
    print("\n🎯 Demo 5: Error Handling", file=sys.stderr)
    print("=" * 50, file=sys.stderr)
    
    try:
        # Test configuration validation errors
        print("🧪 Testing configuration validation...", file=sys.stderr)
        
        try:
            invalid_config = MCPServerConfig(
                name="",  # Invalid: empty name
                port=99999  # Invalid: port out of range
            )
            invalid_config.validate()
            print("❌ Should have failed validation", file=sys.stderr)
        except ValueError as e:
            print(f"✅ Caught expected validation error: {e}", file=sys.stderr)
        
        # Test security errors
        print("🧪 Testing security errors...", file=sys.stderr)
        
        config = MCPServerConfig()
        config.security.auth_method = MCPAuthMethod.API_KEY
        config.security.api_keys = ["valid-key"]
        
        security_manager = MCPSecurityManager(config)
        
        try:
            # Test invalid API key
            security_manager.authenticate({"api_key": "invalid-key"})
            print("❌ Should have failed authentication", file=sys.stderr)
        except Exception as e:
            print(f"✅ Caught expected auth error: {type(e).__name__}", file=sys.stderr)
        
        # Test input validation
        print("🧪 Testing input validation...", file=sys.stderr)
        
        if security_manager.input_validator:
            try:
                # Test oversized request
                large_data = "x" * (11 * 1024 * 1024)  # 11MB
                security_manager.validate_request(large_data)
                print("❌ Should have failed size validation", file=sys.stderr)
            except Exception as e:
                print(f"✅ Caught expected validation error: {type(e).__name__}", file=sys.stderr)
        
        # Test tool execution errors
        print("🧪 Testing tool execution errors...", file=sys.stderr)
        
        server = ZenooMCPServer(config)
        
        try:
            # Test unknown tool
            await server._execute_tool("unknown_tool", {})
            print("❌ Should have failed with unknown tool", file=sys.stderr)
        except Exception as e:
            print(f"✅ Caught expected tool error: {type(e).__name__}", file=sys.stderr)
        
        print("\n✅ Error handling demo completed", file=sys.stderr)
        
    except Exception as e:
        print(f"❌ Error handling demo failed: {e}", file=sys.stderr)
        raise


async def main():
    """Run all MCP server demos."""
    print("🚀 Zenoo RPC MCP Server Demo", file=sys.stderr)
    print("=" * 60, file=sys.stderr)
    print("This demo showcases MCP server capabilities", file=sys.stderr)
    print("Note: Using mock implementations for demonstration", file=sys.stderr)
    print("=" * 60, file=sys.stderr)
    
    demos = [
        demo_basic_server,
        demo_security_configuration,
        demo_configuration_methods,
        demo_server_capabilities,
        demo_error_handling,
    ]
    
    passed = 0
    failed = 0
    
    for demo in demos:
        try:
            await demo()
            passed += 1
            print("✅ Demo completed successfully\n", file=sys.stderr)
        except Exception as e:
            failed += 1
            print(f"❌ Demo failed: {e}\n", file=sys.stderr)
            logger.exception("Demo failed with exception")
    
    print("📊 Demo Results:", file=sys.stderr)
    print(f"✅ Passed: {passed}", file=sys.stderr)
    print(f"❌ Failed: {failed}", file=sys.stderr)
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%", file=sys.stderr)
    
    if failed == 0:
        print("\n🎉 All MCP server demos completed successfully!", file=sys.stderr)
        print("\n🔗 Next Steps:", file=sys.stderr)
        print("1. Install MCP Python SDK: pip install mcp", file=sys.stderr)
        print("2. Configure your Odoo connection", file=sys.stderr)
        print("3. Set up authentication (API keys)", file=sys.stderr)
        print("4. Start the server: python -m zenoo_rpc.mcp_server.cli", file=sys.stderr)
        print("5. Connect AI tools (Claude Desktop, etc.)", file=sys.stderr)
        print("\n📖 See docs/mcp-server-guide.md for detailed documentation", file=sys.stderr)
    else:
        print(f"\n⚠️ {failed} demo(s) failed. Check the logs for details.", file=sys.stderr)


if __name__ == "__main__":
    asyncio.run(main())
