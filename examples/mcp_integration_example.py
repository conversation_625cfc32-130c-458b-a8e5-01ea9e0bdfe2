#!/usr/bin/env python3
"""
MCP Integration Example for Zenoo RPC

This example demonstrates how to use MCP (Model Context Protocol) integration
with Zenoo RPC to extend Odoo operations with external tools and AI services.

Requirements:
- Zenoo RPC with MCP integration
- MCP servers (optional - will use mock servers for demo)
- Odoo instance (optional - will use mock data for demo)

Usage:
    python examples/mcp_integration_example.py
"""

import asyncio
import logging
import sys
import os
from typing import List, Dict, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from zenoo_rpc.mcp import (
    MCPManager,
    MCPServerConfig,
    MCPTransportType,
    MCPError
)
from zenoo_rpc.mcp.integration import MCPIntegration

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DemoZenooClient(MCPIntegration):
    """Demo Zenoo client with MCP integration."""
    
    def __init__(self, host: str = "localhost"):
        super().__init__()
        self.host = host
        self.connected = False
    
    async def __aenter__(self):
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.disconnect()
    
    async def connect(self):
        """Mock connection to Odoo."""
        logger.info(f"Connecting to Odoo at {self.host}")
        self.connected = True
    
    async def disconnect(self):
        """Mock disconnection from Odoo."""
        if self._mcp_enabled:
            await self._cleanup_mcp()
        logger.info("Disconnected from Odoo")
        self.connected = False
    
    async def login(self, database: str, username: str, password: str):
        """Mock login to Odoo."""
        logger.info(f"Logged in to {database} as {username}")
        return True


async def demo_basic_mcp_setup():
    """Demonstrate basic MCP setup and operations."""
    print("\n🎯 Demo 1: Basic MCP Setup")
    print("=" * 50)
    
    try:
        async with DemoZenooClient() as client:
            await client.login("demo", "admin", "admin")
            
            # Setup MCP servers
            await client.setup_mcp_manager([
                MCPServerConfig(
                    name="filesystem",
                    transport_type=MCPTransportType.STDIO,
                    command="echo",  # Mock command for demo
                    args=["filesystem_server"]
                ),
                MCPServerConfig(
                    name="database",
                    transport_type=MCPTransportType.HTTP,
                    url="http://localhost:8000/mcp"
                )
            ])
            
            print("✅ MCP integration setup successfully")
            
            # List servers
            servers = await client.mcp_list_servers()
            print(f"✅ Configured servers: {servers}")
            
            # Check health
            health = await client.mcp_health_check_all()
            print(f"✅ Server health status: {health}")
            
            # List tools (will use mock data)
            tools = await client.mcp_list_all_tools()
            print(f"✅ Available tools: {len(tools)} servers")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise


async def demo_file_operations():
    """Demonstrate file operations using MCP."""
    print("\n🎯 Demo 2: File Operations with MCP")
    print("=" * 50)
    
    try:
        async with DemoZenooClient() as client:
            await client.login("demo", "admin", "admin")
            
            # Setup filesystem MCP server
            await client.setup_mcp_manager([
                MCPServerConfig(
                    name="fs",
                    transport_type=MCPTransportType.STDIO,
                    command="echo",
                    args=["filesystem_mock"]
                )
            ])
            
            # Mock file operations
            print("📁 Performing file operations...")
            
            # List files
            files = await client.mcp_call_tool("fs", "list_files", {"path": "/"})
            print(f"✅ Listed files: {files}")
            
            # Read file
            content = await client.mcp_read_resource("fs", "file:///data/config.json")
            print(f"✅ Read file content: {content}")
            
            # Write file (mock)
            result = await client.mcp_call_tool("fs", "write_file", {
                "path": "/tmp/zenoo_output.txt",
                "content": "Hello from Zenoo RPC + MCP!"
            })
            print(f"✅ Wrote file: {result}")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise


async def demo_database_integration():
    """Demonstrate database integration using MCP."""
    print("\n🎯 Demo 3: Database Integration with MCP")
    print("=" * 50)
    
    try:
        async with DemoZenooClient() as client:
            await client.login("demo", "admin", "admin")
            
            # Setup database MCP server
            await client.setup_mcp_manager([
                MCPServerConfig(
                    name="db",
                    transport_type=MCPTransportType.HTTP,
                    url="http://localhost:8000/mcp"
                )
            ])
            
            print("🗄️ Performing database operations...")
            
            # Execute query
            results = await client.mcp_call_tool("db", "execute_query", {
                "sql": "SELECT * FROM customers WHERE country = ?",
                "params": ["USA"]
            })
            print(f"✅ Query results: {results}")
            
            # Get schema
            schema = await client.mcp_read_resource("db", "schema://public")
            print(f"✅ Database schema: {schema}")
            
            # Bulk insert (mock)
            insert_result = await client.mcp_call_tool("db", "bulk_insert", {
                "table": "analytics",
                "data": [
                    {"metric": "sales", "value": 1000, "date": "2024-01-01"},
                    {"metric": "users", "value": 50, "date": "2024-01-01"}
                ]
            })
            print(f"✅ Bulk insert result: {insert_result}")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise


async def demo_ai_workflow():
    """Demonstrate AI-powered workflow with MCP."""
    print("\n🎯 Demo 4: AI-Powered Workflow")
    print("=" * 50)
    
    try:
        async with DemoZenooClient() as client:
            await client.login("demo", "admin", "admin")
            
            # Setup multiple MCP servers
            await client.setup_mcp_manager([
                MCPServerConfig(
                    name="ai",
                    transport_type=MCPTransportType.HTTP,
                    url="http://localhost:8001/mcp"
                ),
                MCPServerConfig(
                    name="data_processor",
                    transport_type=MCPTransportType.STDIO,
                    command="echo",
                    args=["data_processor"]
                )
            ])
            
            print("🤖 Performing AI-powered workflow...")
            
            # Mock Odoo data
            mock_partners = [
                {"name": "Acme Corp", "country": "USA", "industry": "Technology"},
                {"name": "Global Inc", "country": "UK", "industry": "Finance"},
                {"name": "Tech Solutions", "country": "Germany", "industry": "Technology"}
            ]
            
            # Process data
            processed_data = await client.mcp_call_tool("data_processor", "clean_data", {
                "data": mock_partners,
                "operations": ["normalize_names", "validate_countries"]
            })
            print(f"✅ Processed data: {processed_data}")
            
            # AI analysis
            analysis = await client.mcp_call_tool("ai", "analyze_companies", {
                "companies": processed_data,
                "analysis_type": "market_segmentation"
            })
            print(f"✅ AI analysis: {analysis}")
            
            # Generate insights
            insights = await client.mcp_call_tool("ai", "generate_insights", {
                "analysis": analysis,
                "format": "business_summary"
            })
            print(f"✅ Generated insights: {insights}")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise


async def demo_error_handling():
    """Demonstrate error handling in MCP operations."""
    print("\n🎯 Demo 5: Error Handling")
    print("=" * 50)
    
    try:
        async with DemoZenooClient() as client:
            await client.login("demo", "admin", "admin")
            
            # Setup MCP server
            await client.setup_mcp_manager([
                MCPServerConfig(
                    name="test_server",
                    transport_type=MCPTransportType.STDIO,
                    command="echo",
                    args=["test"]
                )
            ])
            
            print("🛡️ Testing error handling...")
            
            # Test server not found
            try:
                await client.mcp_call_tool("nonexistent", "tool", {})
            except MCPError as e:
                print(f"✅ Caught expected error: {e}")
            
            # Test tool call with invalid arguments
            try:
                result = await client.mcp_call_tool("test_server", "invalid_tool", {
                    "invalid": "arguments"
                })
                print(f"✅ Tool call result (mock): {result}")
            except MCPError as e:
                print(f"✅ Caught tool error: {e}")
            
            # Test resource read
            try:
                content = await client.mcp_read_resource("test_server", "invalid://resource")
                print(f"✅ Resource read result (mock): {content}")
            except MCPError as e:
                print(f"✅ Caught resource error: {e}")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise


async def demo_health_monitoring():
    """Demonstrate health monitoring capabilities."""
    print("\n🎯 Demo 6: Health Monitoring")
    print("=" * 50)
    
    try:
        async with DemoZenooClient() as client:
            await client.login("demo", "admin", "admin")
            
            # Setup multiple servers
            await client.setup_mcp_manager([
                MCPServerConfig(
                    name="server1",
                    transport_type=MCPTransportType.STDIO,
                    command="echo",
                    args=["server1"]
                ),
                MCPServerConfig(
                    name="server2",
                    transport_type=MCPTransportType.HTTP,
                    url="http://localhost:8000/mcp"
                ),
                MCPServerConfig(
                    name="server3",
                    transport_type=MCPTransportType.HTTP,
                    url="http://localhost:8001/mcp"
                )
            ], health_monitoring=True)
            
            print("🏥 Monitoring server health...")
            
            # Check initial health
            health = await client.mcp_health_check_all()
            print(f"✅ Initial health status: {health}")
            
            # List connected servers
            connected = await client.mcp_get_connected_servers()
            print(f"✅ Connected servers: {connected}")
            
            # Simulate some operations
            for server in connected:
                try:
                    tools = await client.mcp_list_all_tools()
                    print(f"✅ {server}: {len(tools.get(server, []))} tools available")
                except Exception as e:
                    print(f"⚠️ {server}: Error listing tools - {e}")
            
            # Final health check
            final_health = await client.mcp_health_check_all()
            print(f"✅ Final health status: {final_health}")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        raise


async def main():
    """Run all MCP integration demos."""
    print("🚀 Zenoo RPC MCP Integration Demo")
    print("=" * 60)
    print("This demo showcases MCP integration capabilities")
    print("Note: Using mock servers for demonstration purposes")
    print("=" * 60)
    
    demos = [
        demo_basic_mcp_setup,
        demo_file_operations,
        demo_database_integration,
        demo_ai_workflow,
        demo_error_handling,
        demo_health_monitoring,
    ]
    
    passed = 0
    failed = 0
    
    for demo in demos:
        try:
            await demo()
            passed += 1
            print("✅ Demo completed successfully\n")
        except Exception as e:
            failed += 1
            print(f"❌ Demo failed: {e}\n")
            logger.exception("Demo failed with exception")
    
    print("📊 Demo Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 All MCP integration demos completed successfully!")
        print("\n🔗 Next Steps:")
        print("1. Install MCP Python SDK: pip install mcp")
        print("2. Setup real MCP servers for your use case")
        print("3. Configure authentication and security")
        print("4. Integrate with your Odoo workflows")
        print("\n📖 See docs/mcp-integration-guide.md for detailed documentation")
    else:
        print(f"\n⚠️ {failed} demo(s) failed. Check the logs for details.")


if __name__ == "__main__":
    asyncio.run(main())
