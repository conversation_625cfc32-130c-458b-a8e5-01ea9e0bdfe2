# Read the Docs configuration file for Zenoo RPC
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

version: 2

# Set the OS, Python version and other tools you might need
build:
  os: ubuntu-22.04
  tools:
    python: "3.11"

# Build documentation in the "docs/" directory with MkDocs
mkdocs:
  configuration: mkdocs.yml
  fail_on_warning: false

# Python requirements for building documentation
python:
  install:
    - method: pip
      path: .
    - requirements: docs/requirements.txt
