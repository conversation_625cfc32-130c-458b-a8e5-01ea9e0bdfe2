"""
Core MCP Server implementation for Zenoo RPC.

This module provides the main MCP server that exposes Odoo operations
through the Model Context Protocol, allowing AI tools to interact with Odoo.
"""

import asyncio
import logging
import json
from typing import Any, Dict, List, Optional, Union
from contextlib import asynccontextmanager

try:
    from mcp.server.fastmcp import FastMCP
    from mcp.types import Tool, Resource, Prompt, TextContent, ImageContent
    MCP_AVAILABLE = True
except ImportError:
    # Mock classes for when MCP is not available
    class FastMCP:
        def __init__(self, name: str, instructions: str = ""):
            self.name = name
            self.instructions = instructions
            self.tools = {}
            self.resources = {}
            self.prompts = {}
        
        def tool(self, name: str = None):
            def decorator(func):
                tool_name = name or func.__name__
                self.tools[tool_name] = func
                return func
            return decorator
        
        def resource(self, uri_template: str):
            def decorator(func):
                self.resources[uri_template] = func
                return func
            return decorator
        
        def prompt(self, name: str = None):
            def decorator(func):
                prompt_name = name or func.__name__
                self.prompts[prompt_name] = func
                return func
            return decorator
        
        async def run(self, transport="stdio"):
            print(f"Mock MCP server '{self.name}' running with {transport} transport")
    
    class Tool:
        def __init__(self, name: str, description: str = ""):
            self.name = name
            self.description = description
    
    class Resource:
        def __init__(self, uri: str, name: str = "", description: str = ""):
            self.uri = uri
            self.name = name
            self.description = description
    
    class Prompt:
        def __init__(self, name: str, description: str = ""):
            self.name = name
            self.description = description
    
    class TextContent:
        def __init__(self, text: str):
            self.text = text
    
    class ImageContent:
        def __init__(self, data: str, mime_type: str):
            self.data = data
            self.mime_type = mime_type
    
    MCP_AVAILABLE = False

from ..client import ZenooClient
from .config import MCPServerConfig
from .security import MCPSecurityManager
from .exceptions import (
    MCPServerError,
    MCPAuthenticationError,
    MCPAuthorizationError,
    MCPToolError,
    MCPResourceError
)

logger = logging.getLogger(__name__)


class ZenooMCPServer:
    """MCP Server that exposes Zenoo RPC/Odoo operations to AI tools.
    
    This server implements the Model Context Protocol to allow AI assistants
    and other tools to interact with Odoo through a standardized interface.
    
    Features:
    - Tools: Execute Odoo operations (CRUD, search, workflows)
    - Resources: Access Odoo data (models, records, reports)
    - Prompts: Template-based Odoo queries
    - Security: Authentication, authorization, input validation
    - Performance: Caching, connection pooling, async operations
    
    Example:
        >>> config = MCPServerConfig.from_env()
        >>> server = ZenooMCPServer(config)
        >>> await server.start()
    """
    
    def __init__(self, config: MCPServerConfig):
        """Initialize MCP server.
        
        Args:
            config: Server configuration
        """
        self.config = config
        self.zenoo_client: Optional[ZenooClient] = None
        self.security_manager = MCPSecurityManager(config)
        
        # Initialize FastMCP server
        self.mcp_server = FastMCP(
            name=config.name,
            instructions=self._get_server_instructions()
        )
        
        # Register tools, resources, and prompts
        self._register_tools()
        self._register_resources()
        self._register_prompts()
        
        logger.info(f"Initialized MCP server '{config.name}'")
    
    def _get_server_instructions(self) -> str:
        """Get server instructions for AI clients."""
        return f"""
{self.config.description}

This server provides access to Odoo ERP operations through the following capabilities:

TOOLS (Actions you can perform):
- search_records: Search for records in any Odoo model
- get_record: Get a specific record by ID
- create_record: Create a new record
- update_record: Update an existing record
- delete_record: Delete a record
- execute_workflow: Execute workflow actions
- generate_report: Generate Odoo reports

RESOURCES (Data you can access):
- odoo://models - List all available Odoo models
- odoo://model/{{model_name}} - Get model information
- odoo://record/{{model_name}}/{{record_id}} - Get specific record
- odoo://search/{{model_name}}/{{domain}} - Search results

PROMPTS (Templates you can use):
- analyze_data: Analyze Odoo data with AI
- generate_report_query: Generate report queries
- suggest_workflow: Suggest workflow improvements

Authentication: {self.config.security.auth_method.value}
Rate Limits: {self.config.security.rate_limit_requests} requests per {self.config.security.rate_limit_window}s
"""
    
    async def start(self) -> None:
        """Start the MCP server."""
        try:
            # Connect to Odoo
            await self._connect_to_odoo()
            
            # Start security cleanup task
            asyncio.create_task(self._security_cleanup_task())
            
            # Run MCP server
            transport = self.config.transport_type.value
            if transport == "stdio":
                await self.mcp_server.run("stdio")
            elif transport == "http":
                await self.mcp_server.run(
                    "http",
                    host=self.config.host,
                    port=self.config.port
                )
            else:
                raise MCPServerError(f"Unsupported transport: {transport}")
                
        except Exception as e:
            logger.error(f"Failed to start MCP server: {e}")
            raise MCPServerError(f"Server startup failed: {e}") from e
    
    async def stop(self) -> None:
        """Stop the MCP server."""
        try:
            if self.zenoo_client:
                await self.zenoo_client.disconnect()
            logger.info("MCP server stopped")
        except Exception as e:
            logger.error(f"Error stopping MCP server: {e}")
    
    async def _connect_to_odoo(self) -> None:
        """Connect to Odoo using Zenoo RPC."""
        try:
            self.zenoo_client = ZenooClient(self.config.odoo_url)
            await self.zenoo_client.connect()
            await self.zenoo_client.login(
                self.config.odoo_database,
                self.config.odoo_username,
                self.config.odoo_password
            )
            logger.info("Connected to Odoo successfully")
        except Exception as e:
            logger.error(f"Failed to connect to Odoo: {e}")
            raise MCPServerError(f"Odoo connection failed: {e}") from e
    
    async def _security_cleanup_task(self) -> None:
        """Background task to clean up expired sessions."""
        while True:
            try:
                await asyncio.sleep(300)  # Clean up every 5 minutes
                self.security_manager.cleanup_expired_sessions()
            except Exception as e:
                logger.error(f"Error in security cleanup task: {e}")
    
    def _register_tools(self) -> None:
        """Register MCP tools."""
        if not self.config.features.enable_tools:
            return
        
        @self.mcp_server.tool()
        async def search_records(
            model: str,
            domain: List = None,
            fields: List[str] = None,
            limit: int = 100,
            offset: int = 0,
            order: str = None
        ) -> Dict[str, Any]:
            """Search for records in an Odoo model.
            
            Args:
                model: Odoo model name (e.g., 'res.partner', 'sale.order')
                domain: Search domain (e.g., [['name', 'ilike', 'John']])
                fields: Fields to retrieve (default: all)
                limit: Maximum number of records (default: 100)
                offset: Number of records to skip (default: 0)
                order: Sort order (e.g., 'name ASC')
            
            Returns:
                Dictionary with search results and metadata
            """
            return await self._execute_tool("search_records", {
                "model": model,
                "domain": domain or [],
                "fields": fields,
                "limit": limit,
                "offset": offset,
                "order": order
            })
        
        @self.mcp_server.tool()
        async def get_record(
            model: str,
            record_id: int,
            fields: List[str] = None
        ) -> Dict[str, Any]:
            """Get a specific record by ID.
            
            Args:
                model: Odoo model name
                record_id: Record ID
                fields: Fields to retrieve (default: all)
            
            Returns:
                Record data
            """
            return await self._execute_tool("get_record", {
                "model": model,
                "record_id": record_id,
                "fields": fields
            })
        
        @self.mcp_server.tool()
        async def create_record(
            model: str,
            values: Dict[str, Any]
        ) -> Dict[str, Any]:
            """Create a new record.
            
            Args:
                model: Odoo model name
                values: Field values for the new record
            
            Returns:
                Created record data
            """
            return await self._execute_tool("create_record", {
                "model": model,
                "values": values
            })
        
        @self.mcp_server.tool()
        async def update_record(
            model: str,
            record_id: int,
            values: Dict[str, Any]
        ) -> Dict[str, Any]:
            """Update an existing record.
            
            Args:
                model: Odoo model name
                record_id: Record ID to update
                values: Field values to update
            
            Returns:
                Updated record data
            """
            return await self._execute_tool("update_record", {
                "model": model,
                "record_id": record_id,
                "values": values
            })
        
        @self.mcp_server.tool()
        async def delete_record(
            model: str,
            record_id: int
        ) -> Dict[str, Any]:
            """Delete a record.
            
            Args:
                model: Odoo model name
                record_id: Record ID to delete
            
            Returns:
                Deletion confirmation
            """
            return await self._execute_tool("delete_record", {
                "model": model,
                "record_id": record_id
            })
    
    def _register_resources(self) -> None:
        """Register MCP resources."""
        if not self.config.features.enable_resources:
            return
        
        @self.mcp_server.resource("odoo://models")
        async def list_models() -> str:
            """List all available Odoo models."""
            return await self._execute_resource("list_models", {})
        
        @self.mcp_server.resource("odoo://model/{model_name}")
        async def get_model_info(model_name: str) -> str:
            """Get information about a specific model."""
            return await self._execute_resource("get_model_info", {
                "model_name": model_name
            })
        
        @self.mcp_server.resource("odoo://record/{model_name}/{record_id}")
        async def get_record_resource(model_name: str, record_id: int) -> str:
            """Get a specific record as a resource."""
            return await self._execute_resource("get_record_resource", {
                "model_name": model_name,
                "record_id": record_id
            })
    
    def _register_prompts(self) -> None:
        """Register MCP prompts."""
        if not self.config.features.enable_prompts:
            return
        
        @self.mcp_server.prompt()
        async def analyze_data(
            model: str = "res.partner",
            analysis_type: str = "summary"
        ) -> str:
            """Generate a prompt for analyzing Odoo data."""
            return await self._execute_prompt("analyze_data", {
                "model": model,
                "analysis_type": analysis_type
            })
    
    async def _execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool with security and validation."""
        try:
            # Security validation would go here
            # For now, simplified implementation
            
            if not self.zenoo_client:
                raise MCPToolError("Not connected to Odoo")
            
            if tool_name == "search_records":
                return await self._handle_search_records(arguments)
            elif tool_name == "get_record":
                return await self._handle_get_record(arguments)
            elif tool_name == "create_record":
                return await self._handle_create_record(arguments)
            elif tool_name == "update_record":
                return await self._handle_update_record(arguments)
            elif tool_name == "delete_record":
                return await self._handle_delete_record(arguments)
            else:
                raise MCPToolError(f"Unknown tool: {tool_name}")
                
        except Exception as e:
            logger.error(f"Tool execution failed: {e}")
            raise MCPToolError(f"Tool '{tool_name}' failed: {e}") from e
    
    async def _handle_search_records(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle search_records tool."""
        # Mock implementation - in real version, use self.zenoo_client
        return {
            "records": [
                {"id": 1, "name": "Sample Record 1"},
                {"id": 2, "name": "Sample Record 2"}
            ],
            "count": 2,
            "model": args["model"]
        }
    
    async def _handle_get_record(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle get_record tool."""
        # Mock implementation
        return {
            "id": args["record_id"],
            "name": f"Record {args['record_id']}",
            "model": args["model"]
        }
    
    async def _handle_create_record(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle create_record tool."""
        # Mock implementation
        return {
            "id": 123,
            "values": args["values"],
            "model": args["model"],
            "created": True
        }
    
    async def _handle_update_record(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle update_record tool."""
        # Mock implementation
        return {
            "id": args["record_id"],
            "values": args["values"],
            "model": args["model"],
            "updated": True
        }
    
    async def _handle_delete_record(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle delete_record tool."""
        # Mock implementation
        return {
            "id": args["record_id"],
            "model": args["model"],
            "deleted": True
        }
    
    async def _execute_resource(self, resource_name: str, arguments: Dict[str, Any]) -> str:
        """Execute a resource request."""
        # Mock implementation
        return json.dumps({
            "resource": resource_name,
            "arguments": arguments,
            "data": "Mock resource data"
        })
    
    async def _execute_prompt(self, prompt_name: str, arguments: Dict[str, Any]) -> str:
        """Execute a prompt request."""
        # Mock implementation
        return f"Mock prompt for {prompt_name} with args: {arguments}"
