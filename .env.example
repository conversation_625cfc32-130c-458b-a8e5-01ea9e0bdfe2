# Zenoo-RPC Environment Configuration Template
# Copy this file to .env and fill in your actual values
# DO NOT commit .env file to version control

# =============================================================================
# ODOO SERVER CONFIGURATION
# =============================================================================

# Odoo server URL (without trailing slash)
ODOO_HOST=https://demo.odoo.com

# Database name
ODOO_DATABASE=demo_database

# Username for authentication
ODOO_USERNAME=demo_user

# Password for authentication
ODOO_PASSWORD=demo_password

# =============================================================================
# ENVIRONMENT SPECIFIC CONFIGURATIONS
# =============================================================================

# Current environment (development, staging, production)
ENVIRONMENT=development

# Development environment
ODOO_DEV_HOST=https://dev.odoo.com
ODOO_DEV_DATABASE=dev_database
ODOO_DEV_USERNAME=dev_user
ODOO_DEV_PASSWORD=dev_password

# Staging environment
ODOO_STAGING_HOST=https://staging.odoo.com
ODOO_STAGING_DATABASE=staging_database
ODOO_STAGING_USERNAME=staging_user
ODOO_STAGING_PASSWORD=staging_password

# Production environment
ODOO_PROD_HOST=https://prod.odoo.com
ODOO_PROD_DATABASE=prod_database
ODOO_PROD_USERNAME=prod_user
ODOO_PROD_PASSWORD=prod_password

# =============================================================================
# REDIS CONFIGURATION (Optional)
# =============================================================================

# Redis URL for caching
REDIS_URL=redis://localhost:6379/0

# Redis password (if required)
REDIS_PASSWORD=

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Enable/disable real server testing
USE_REAL_SERVER=false

# Test database configuration
TEST_ODOO_HOST=https://test.odoo.com
TEST_ODOO_DATABASE=test_database
TEST_ODOO_USERNAME=test_user
TEST_ODOO_PASSWORD=test_password

# =============================================================================
# PERFORMANCE TESTING
# =============================================================================

# Performance test iterations
PERF_TEST_ITERATIONS=100

# Concurrent users for performance testing
PERF_CONCURRENT_USERS=10

# Performance test timeout (seconds)
PERF_TEST_TIMEOUT=300

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log file path
LOG_FILE=logs/zenoo-rpc.log

# Enable/disable debug mode
DEBUG=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# API key for authentication (if required)
API_KEY=

# JWT secret key (if using JWT authentication)
JWT_SECRET_KEY=

# Session timeout (seconds)
SESSION_TIMEOUT=3600

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Cache backend (memory, redis)
CACHE_BACKEND=memory

# Cache TTL (seconds)
CACHE_TTL=300

# Cache max size (for memory backend)
CACHE_MAX_SIZE=10000

# =============================================================================
# DATABASE CONFIGURATION (if using local database)
# =============================================================================

# Database URL for local storage
DATABASE_URL=sqlite:///zenoo_rpc.db

# Database pool size
DB_POOL_SIZE=20

# Database timeout
DB_TIMEOUT=30

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Enable/disable metrics collection
ENABLE_METRICS=true

# Metrics endpoint
METRICS_ENDPOINT=/metrics

# Health check endpoint
HEALTH_ENDPOINT=/health

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Enable/disable auto-reload
AUTO_RELOAD=true

# Enable/disable profiling
ENABLE_PROFILING=false

# Enable/disable tracing
ENABLE_TRACING=false

# =============================================================================
# EXAMPLE VALUES FOR QUICK TESTING
# =============================================================================

# Uncomment and modify these for quick testing:
# ODOO_HOST=https://demo.odoo.com
# ODOO_DATABASE=demo
# ODOO_USERNAME=admin
# ODOO_PASSWORD=admin

# =============================================================================
# NOTES
# =============================================================================

# 1. Copy this file to .env: cp .env.example .env
# 2. Fill in your actual values
# 3. Never commit .env file to version control
# 4. Use strong passwords for production environments
# 5. Consider using secrets management for production
# 6. Rotate credentials regularly
# 7. Use different credentials for different environments
