name: Publish to PyPI

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      test_pypi:
        description: 'Publish to Test PyPI instead of PyPI'
        required: false
        default: false
        type: boolean

permissions:
  contents: read
  id-token: write  # Required for trusted publishing

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for setuptools_scm

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: |
        python -m build

    - name: Check package
      run: |
        twine check dist/*

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  test-install:
    needs: build
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.9", "3.12"]

    steps:
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Test wheel installation
      run: |
        pip install dist/*.whl
        python -c "import zenoo_rpc; print(f'Zenoo RPC {zenoo_rpc.__version__} installed successfully')"

  publish-test-pypi:
    if: github.event.inputs.test_pypi == 'true'
    needs: [build, test-install]
    runs-on: ubuntu-latest
    environment:
      name: test-pypi
      url: https://test.pypi.org/p/zenoo-rpc

    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Publish to Test PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: https://test.pypi.org/legacy/

  publish-pypi:
    if: github.event_name == 'release'
    needs: [build, test-install]
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/p/zenoo-rpc

    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Publish to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1

  create-github-release:
    if: github.event_name == 'release'
    needs: publish-pypi
    runs-on: ubuntu-latest
    permissions:
      contents: write

    steps:
    - uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Upload release assets
      uses: softprops/action-gh-release@v1
      with:
        files: dist/*
        generate_release_notes: true
